/* Offer Details Styles */
.OfferDetails {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
}

.OfferDetails__back-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: none;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  color: var(--secondary-color);
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.OfferDetails__back-btn:hover {
  background-color: var(--primary-light-color);
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.OfferDetails__title {
  margin: 0;
  font-size: var(--heading2);
  color: var(--secondary-color);
}

.OfferDetails__content {
  display: grid;
  gap: 24px;
}

.OfferDetails__main-section {
  display: grid;
  gap: 20px;
}

/* Card Styles */
.OfferDetails__content-card,
.OfferDetails__offer-card,
.OfferDetails__buyer-card,
.OfferDetails__message-card,
.OfferDetails__response-card {
  background: white;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius-medium);
  padding: 20px;
}

/* Content Card */
.OfferDetails__content-info {
  display: flex;
  gap: 20px;
  align-items: flex-start;
}

.OfferDetails__content-image {
  width: 100px;
  height: 80px;
  object-fit: cover;
  border-radius: var(--border-radius);
  flex-shrink: 0;
  object-fit: cover;
}

.OfferDetails__content-details {
  flex: 1;
}

.OfferDetails__content-title {
  margin: 0 0 8px 0;
  font-size: var(--heading6);
  color: var(--secondary-color);
}

.OfferDetails__content-sport {
  margin: 0 0 8px 0;
  color: var(--dark-gray);
  font-size: var(--smallfont);
  text-transform: capitalize;
}

.OfferDetails__content-price {
  margin: 0;
  font-size: var(--smallfont);
  font-weight: 600;
  color: var(--primary-color);
}

/* Info Grid */
.OfferDetails__info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.OfferDetails__info-item {
  display: flex;
  align-items: center;
  gap: 4px;
  flex-wrap: wrap;
}

.OfferDetails__info-item label {
  font-weight: 600;
  color: var(--secondary-color);
  font-size: var(--smallfont);
}

.OfferDetails__info-item span {
  color: var(--dark-gray);
  font-size: var(--smallfont);
}

.OfferDetails__amount {
  font-size: var(--mediumfont) !important;
  font-weight: 600 !important;
  color: var(--primary-color) !important;
}

/* Buyer Info */
.OfferDetails__buyer-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

/* Message Content */
.OfferDetails__message-content,
.OfferDetails__response-content {
  background-color: var(--primary-light-color);
  padding: 16px;
  border-radius: var(--border-radius);
  border-left: 4px solid var(--primary-color);
  font-style: italic;
  color: var(--secondary-color);
  line-height: 1.5;
}

.OfferDetails__response-content {
  border-left-color: var(--success-color);
  background-color: #f0f9f0;
}

/* Card Headers */
.OfferDetails__content-card h4,
.OfferDetails__offer-card h4,
.OfferDetails__buyer-card h4,
.OfferDetails__message-card h4,
.OfferDetails__response-card h4 {
  margin: 0 0 16px 0;
  font-size: var(--heading5);
  color: var(--secondary-color);
  border-bottom: 1px solid var(--light-gray);
  padding-bottom: 8px;
}

/* Status Badge */
.OfferDetails .status-badge {
  padding: 4px 12px;
  border-radius: var(--border-radius);
  font-size: var(--extrasmallfont);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.OfferDetails .status-pending {
  background-color: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
  width: fit-content;
}

.OfferDetails .status-accepted {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
  width: fit-content;
}

.OfferDetails .status-rejected {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
  width: fit-content;
}

.OfferDetails .status-cancelled {
  background-color: #e2e3e5;
  color: #383d41;
  border: 1px solid #d6d8db;
  width: fit-content;
}

.OfferDetails .status-expired {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
  width: fit-content;
}

/* Action Buttons */
.OfferDetails__actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  padding: 24px;
  background: white;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius-medium);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.OfferDetails__actions button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border: none;
  border-radius: var(--border-radius);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 140px;
  justify-content: center;
}

.OfferDetails__actions .btn-success {
  background-color: #218838;
  color: white;
}

.OfferDetails__actions .btn-success:hover:not(:disabled) {
  background-color: #196b2b;
}

.OfferDetails__actions .btn-danger {
  background-color: #dc3545;
  color: white;
}

.OfferDetails__actions .btn-danger:hover:not(:disabled) {
  background-color: #c82333;
}

.OfferDetails__actions button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Error State */
.OfferDetails__error {
  text-align: center;
  padding: 48px 24px;
  background: white;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius-medium);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.OfferDetails__error h3 {
  margin: 0 0 16px 0;
  color: var(--secondary-color);
}

.OfferDetails__error p {
  margin: 0 0 24px 0;
  color: var(--dark-gray);
}

/* Modal Styles */
.OfferDetails__modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.OfferDetails__response-modal {
  background: white;
  border-radius: var(--border-radius-medium);
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.OfferDetails__modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 24px 0;
  border-bottom: 1px solid var(--light-gray);
  margin-bottom: 24px;
}

.OfferDetails__modal-header h3 {
  margin: 0;
  font-size: var(--heading4);
  color: var(--secondary-color);
}

.OfferDetails__modal-close {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  color: var(--dark-gray);
  padding: 4px;
  border-radius: var(--border-radius);
  transition: background-color 0.2s;
}

.OfferDetails__modal-close:hover {
  background-color: var(--light-gray);
}

.OfferDetails__modal-body {
  padding: 0 24px 24px;
}

.OfferDetails__offer-summary {
  background-color: var(--primary-light-color);
  padding: 16px;
  border-radius: var(--border-radius);
  margin-bottom: 20px;
}

.OfferDetails__offer-summary p {
  margin: 0 0 8px 0;
  font-size: var(--smallfont);
}

.OfferDetails__offer-summary p:last-child {
  margin-bottom: 0;
}

.OfferDetails__response-input {
  margin-bottom: 24px;
}

.OfferDetails__response-input label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: var(--secondary-color);
}

.OfferDetails__response-input textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-family: inherit;
  font-size: var(--smallfont);
  resize: vertical;
  min-height: 100px;
}

.OfferDetails__response-input textarea:focus {
  outline: none;
  border-color: var(--primary-color);
}

.OfferDetails__response-input small {
  color: var(--dark-gray);
  font-size: var(--extrasmallfont);
}

.OfferDetails__modal-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding: 20px 24px;
  border-top: 1px solid var(--light-gray);
}

.OfferDetails__modal-actions button {
  padding: 10px 20px;

  border-radius: var(--border-radius);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

/* .OfferDetails__modal-actions .btn-outline {
  background: white;
  border: 1px solid var(--light-gray);
  color: var(--secondary-color);
} */

/* .OfferDetails__modal-actions .btn-outline:hover:not(:disabled) {
  background-color: var(--light-gray);
} */

.OfferDetails__modal-actions .btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.OfferDetails__modal-actions .btn-primary:hover:not(:disabled) {
  background-color: var(--primary-dark-color);
}

.OfferDetails__modal-actions .btn-danger {
  background-color: #dc3545;
  color: white;
}

.OfferDetails__modal-actions .btn-danger:hover:not(:disabled) {
  background-color: #c82333;
}

.OfferDetails__modal-actions button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 768px) {
  .OfferDetails__content-info {
    gap: 16px;
  }

  .OfferDetails__info-grid,
  .OfferDetails__buyer-info {
    grid-template-columns: 1fr;
  }

  .OfferDetails__actions {
    flex-direction: column;
  }

  .OfferDetails__actions button {
    width: 100%;
  }

  .OfferDetails__response-modal {
    width: 95%;
    margin: 20px;
  }

  .OfferDetails__modal-actions {
    flex-direction: column;
  }

  .OfferDetails__modal-actions button {
    width: 100%;
  }
  .OfferDetails__content-card,
  .OfferDetails__offer-card,
  .OfferDetails__buyer-card,
  .OfferDetails__message-card,
  .OfferDetails__response-card {
    background: white;
    border: 1px solid var(--light-gray);
    border-radius: var(--border-radius-medium);
    padding: 20px 10px;
  }
}
@media (max-width: 300px) {
  .OfferDetails__content-info {
    gap: 16px;
    flex-direction: column;
  }
}
