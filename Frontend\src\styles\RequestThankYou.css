/* Request Thank You Page Styles */
.request-thank-you-page {

  
  padding: 40px 0px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.request-thank-you-page .request-thank-you-container {
  width: 100%;
  max-width: 900px;
  padding: 0 20px;
}

/* Success Header */
.request-thank-you-page .success-header {
  text-align: center;
  margin-bottom: 40px;
   display: flex
;
    flex-direction: column;
    align-content: center;
    justify-content: center;
    align-items: center;
    width: 100%;
}

.request-thank-you-page .success-icon {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

.request-thank-you-page .success-icon img {
  width: 64px;
  height: 64px;
   animation: zoomInOut 2s ease-in-out infinite;
}

.request-thank-you-page .success-title {
  font-size: var(--heading3);
  font-weight: 600;
  color: var(--secondary-color);
  margin-bottom: 10px;
}

.request-thank-you-page .success-message {
  font-size: var(--basefont);
  color: var(--dark-gray);
  max-width: 500px;
  margin: 0 auto;
}

/* Request Information Card */
.request-thank-you-page .request-info-card {
  background-color: var(--white);
  border-radius: var(--border-radius-large);
border: 1px solid var(--light-gray);
  padding: 30px;
  margin-bottom: 30px;
}

.request-thank-you-page .request-info-title {
  font-size: var(--heading6);
  font-weight: 600;
  color: var(--secondary-color);
  margin-bottom: 20px;
  
}
.request-thank-you-page .reqthankyoucontainer{
   
    display: grid;
    gap: 2rem;
    justify-content: space-between;
    align-items: center;
    grid-template-columns: 1fr 1px 1fr;
    border-bottom: 2px solid var(--light-gray);
    margin-bottom: 30px;
    padding-bottom: 30px;

}
.request-thank-you-page .verticalline{
  height: 100%;
  background-color: var(--light-gray);
}
/* Request Details Grid */
.request-thank-you-page .request-details-grid {
  display: grid;
  grid-template-columns: 1fr;
 gap: 10px; 
 
  
}

.request-thank-you-page .request-detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
}

.request-thank-you-page .detail-label {
  font-size: var(--basefont);
  font-weight: 500;
  color: var(--dark-gray);
}
.request-thank-you-page .detail-label::after{
  content: ":";
  margin-left: 4px;
}
.request-thank-you-page .detail-value {
  font-size: var(--basefont);
  font-weight: 600;
  color: var(--secondary-color);
}



.request-thank-you-page .section-title {
  font-size: var(--heading6);
  font-weight: 600;
  color: var(--secondary-color);
  margin-bottom: 20px;
}

.request-thank-you-page .item-info-content {
  display: flex;
  align-items: center;
  gap: 20px;

  border-radius: var(--border-radius-medium);
  
}

.request-thank-you-page .item-image {
  width: 80px;
  height: 80px;
  border-radius: var(--border-radius-medium);
  object-fit: cover;
}

.request-thank-you-page .item-details {
  flex: 1;
}

.request-thank-you-page .item-title {
  font-size: var(--basefont);
  font-weight: 600;
  color: var(--secondary-color);
  margin-bottom: 8px;
  line-height: 1.4;
}

.request-thank-you-page .item-author {
  font-size: var(--smallfont);
  color: var(--dark-gray);
}

/* Action Buttons */
.request-thank-you-page .action-buttons {
  display: flex;
  gap: 20px;
  justify-content: center;
  align-items: center;
}

.request-thank-you-page .request-btn {
  padding: 12px 24px;
  font-size: var(--basefont);
  font-weight: 500;
  border-radius: var(--border-radius-medium);
  transition: all 0.3s ease;
}

.request-thank-you-page .request-btn:hover {
  background-color: var(--btn-color);
  color: var(--white);
 transform: scale(1.02);
  box-shadow: var(--box-shadow);
}

.request-thank-you-page .homepage-btn {
  padding: 12px 24px;
  font-size: var(--basefont);
  font-weight: 600;
  border-radius: var(--border-radius-medium);
  background-color: var(--btn-color);
  color: var(--white);
  transition: all 0.3s ease;
}

.request-thank-you-page .homepage-btn:hover {
  background: linear-gradient(
    to bottom,
    var(--primary-color),
    var(--btn-color)
  );
 transform: scale(1.02);
  box-shadow: var(--box-shadow);
}

/* Responsive Design */
@media (max-width: 768px) {
  .request-thank-you-page {
    padding: 30px 0;
  }

  .request-thank-you-page .request-info-card {
    padding: 30px 20px;
  }

  .request-thank-you-page .request-details-grid {
    grid-template-columns: 1fr;
   
  }



  .request-thank-you-page .action-buttons {
    flex-direction: column;
    gap: 15px;
  }

  .request-thank-you-page .action-buttons .btn {
    width: 100%;
    justify-content: center;
  }

  .request-thank-you-page .success-icon img {
    width: 50px;
    height: 50px;
  }

  .request-thank-you-page .success-title {
    font-size: var(--heading4);
  }


  .request-thank-you-page .reqthankyoucontainer{
   
    display: grid;
   gap: 5px;
grid-template-columns: 1fr;

}
}

@media (max-width: 480px) {
  .request-thank-you-page .request-info-card {
    padding: 20px 15px;
  }


  .request-thank-you-page .request-info-title {

  margin-bottom: 20px;
  
}
}
@keyframes zoomInOut {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
}