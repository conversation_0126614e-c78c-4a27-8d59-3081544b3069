/* SellerMySportsStrategies.css */

.video-status-container {
  padding: var(--section-padding);
  background-color: var(--white);
  font-family: "Poppins", sans-serif;
}

.video-status-container .video-table {
  width: 100%;

  font-size: var(--basefont);
  background-color: var(--white);

  border-radius: var(--border-radius-large);
}

.video-status-container .video-table th {
  padding: 12px 10px;
  text-align: left;

  vertical-align: middle;
}
.video-status-container .video-table td {
  padding: 12px 10px;
  text-align: left;
  border-top: 1px solid var(--light-gray);
  vertical-align: middle;
}
.video-status-container .video-doc {
  display: flex;
  align-items: center;
  gap: 12px;
}

.video-status-container .video-thumbnail {
  width: 50px;
  height: 35px;
  border-radius: var(--border-radius);
  overflow: hidden;
  flex-shrink: 0;
}

.video-status-container .video-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.video-status-container .placeholder-thumb {
  width: 100%;
  height: 100%;
  background: var(--light-gray);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: var(--text-color);
}

.video-status-container .video-title {
  font-size: var(--basefont);
  font-weight: 500;
  color: var(--text-color);
  line-height: 1.4;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
}

/* Toggle Switch */
.video-status-container .switch {
  position: relative;
  display: inline-block;
  width: 35px;
  height: 18px;
}

.video-status-container .switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.video-status-container .slider {
  position: absolute;
  cursor: pointer;
  background-color: var(--light-gray);
  border-radius: 22px;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  transition: 0.4s;
}

.video-status-container .slider::before {
  position: absolute;
  content: "";
  height: 12px;
  width: 12px;
  left: 2px;
  bottom: 1.8px;
  background-color: var(--white);
  border-radius: 50%;
  transition: 0.4s;
}

.video-status-container input:checked + .slider {
  background-color: var(--btn-color);
}

.video-status-container input:checked + .slider::before {
  transform: translateX(18px);
}

.video-status-container .slider.round {
  border-radius: 34px;
}

/* Action Icons Container */
.video-status-container .action-icon-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Base Action Icon Styles */
.video-status-container .action-icon {
  font-size: var(--heading6) !important;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
}

/* Eye Icon (View) */
.video-status-container .eyeicon {
  color: black !important;
  background-color: transparent !important;
  font-size: var(--heading6) !important;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-status-container .eyeicon:hover {
  transform: scale(1.02);
  color: black !important;
  background-color: transparent !important;
}

/* Edit Icon */
.video-status-container .edit-icon {
  color: black !important;
  background-color: transparent !important;
  font-size: var(--heading6) !important;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-status-container .edit-icon:hover {
  transform: scale(1.02);
  color: black !important;
  background-color: transparent !important;
}

/* Delete Icon */
.delete-icon {
  color: black;
  background-color: transparent;
  font-size: var(--heading6);
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Loading and Empty States */
.video-status-container .loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.video-status-container .loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--light-gray);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.video-status-container .loading-container p {
  color: var(--text-color);
  font-size: var(--basefont);
  margin: 0;
}

.video-status-container .empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px 20px;
  text-align: center;
  background: var(--light-background);
  border-radius: var(--border-radius-large);
  border: 2px dashed var(--light-gray);
}

.video-status-container .empty-state h3 {
  color: var(--text-color);
  font-size: var(--heading5);
  margin: 0 0 12px 0;
  font-weight: 600;
}

.video-status-container .empty-state p {
  color: var(--text-light);
  font-size: var(--basefont);
  margin: 0;
  max-width: 400px;
  line-height: 1.5;
}

/* Disabled state for toggle switch */
.video-status-container input:disabled + .slider {
  opacity: 0.6;
  cursor: not-allowed;
}

.video-status-container input:disabled + .slider::before {
  cursor: not-allowed;
}
