.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-index-modal);
  backdrop-filter: blur(4px);
}

.modal-content {
  background: var(--white);
  padding: 2rem;
  border-radius: var(--border-radius-large);
  box-shadow: var(--box-shadow);
  width: 90%;
  max-width: 500px;
  position: relative;
  animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal-content h2 {
  margin: 0 0 1.5rem 0;
  color: var(--secondary-color);
  font-size: var(--heading5);
  font-weight: 600;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: var(--text-color);
  font-weight: 500;
  font-size: var(--basefont);
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--basefont);
  color: var(--text-color);
  transition: all 0.2s ease;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--btn-color);
  box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.1);
}

.form-group textarea {
  min-height: 120px;
  resize: vertical;
}

.rating-input {
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.rating-input label {
  color: var(--text-color);
  font-weight: 500;
  margin: 0;
  font-size: var(--basefont);
}

.modal-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 2rem;
}

.right-actions {
  display: flex;
  gap: 1rem;
}

.delete-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: var(--border-radius);
  font-size: var(--basefont);
  font-weight: 500;
  cursor: pointer;
  background: var(--white);
  border: 1px solid var(--error-color);
  color: var(--error-color);
  transition: all 0.2s ease;
}

.delete-button:hover {
  background: var(--error-color);
  color: var(--white);
}

.modal-actions button[type="button"]:not(.delete-button) {
  background: transparent;
  border: 1px solid var(--light-gray);
  color: var(--dark-gray);
  padding: 0.75rem 1.5rem;
  border-radius: var(--border-radius);
  font-size: var(--basefont);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.modal-actions button[type="button"]:not(.delete-button):hover {
  background: var(--bg-gray);
  border-color: var(--dark-gray);
}

.modal-actions button[type="submit"] {
  background: var(--btn-color);
  border: none;
  color: var(--white);
  padding: 0.75rem 1.5rem;
  border-radius: var(--border-radius);
  font-size: var(--basefont);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.modal-actions button[type="submit"]:hover {
  background: linear-gradient(
    to bottom,
    var(--primary-color),
    var(--btn-color)
  );
  transform: scale(1.02);
}

/* Close button */
.modal-close {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  color: var(--dark-gray);
  transition: color 0.2s ease;
}

.modal-close:hover {
  color: var(--text-color);
}

/* Responsive adjustments */
@media (max-width: 600px) {
  .modal-content {
    width: 95%;
    padding: 1.5rem;
  }

  .modal-actions {
    flex-direction: column;
    gap: 1rem;
  }

  .right-actions {
    width: 100%;
  }

  .modal-actions button {
    width: 100%;
  }

  .delete-button {
    width: 100%;
    justify-content: center;
  }
}

