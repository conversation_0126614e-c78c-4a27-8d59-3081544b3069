import React from "react";
import "../../styles/Table.css";

/**
 * Reusable Table Component
 * A flexible table component that maintains existing UI while reducing code duplication
 *
 * @param {Object} props
 * @param {Array} props.columns - Array of column configurations
 * @param {Array} props.data - Array of data to display
 * @param {Function} props.onRowClick - Optional click handler for rows
 * @param {Function} props.renderRow - Optional custom row renderer
 * @param {string} props.className - Additional CSS classes
 * @param {string} props.emptyMessage - Message to show when no data
 * @param {string} props.variant - Table style variant (default, grid, admin, or custom)
 * @param {string} props.gridTemplate - Grid template columns for responsive layout
 * @param {boolean} props.isAdmin - Whether this is an admin table (applies admin-specific styling)
 * @param {Object} props.loading - Loading state object
 * @param {boolean} props.loading.isLoading - Whether table is loading
 * @param {string} props.loading.message - Loading message to display
 * @param {Function} props.onSort - Optional sort handler for columns
 * @param {Object} props.sortConfig - Current sort configuration
 * @param {string} props.sortConfig.key - Column key being sorted
 * @param {string} props.sortConfig.direction - Sort direction (asc/desc)
 */
const Table = ({
  columns,
  data,
  onRowClick,
  renderRow,
  className = "",
  emptyMessage = "No data available",
  variant = "default",
  gridTemplate = "",
  isAdmin = false,
  loading = { isLoading: false, message: "Loading..." },
  onSort,
  sortConfig = { key: null, direction: null },
}) => {
  const handleRowClick = (item) => {
    if (onRowClick) {
      onRowClick(item);
    }
  };

  const handleSort = (columnKey) => {
    if (onSort) {
      onSort(columnKey);
    }
  };

  const getSortIcon = (columnKey) => {
    if (!onSort || sortConfig.key !== columnKey) return null;
    return sortConfig.direction === 'asc' ? '↑' : '↓';
  };

  // Apply admin-specific classes
  const tableClasses = `table-container ${variant} ${isAdmin ? 'admin-table' : ''} ${className}`;

  return (
    <div className={tableClasses}>
      {variant === "grid" ? (
        // Grid-based table layout (used in BuyerAccountDashboard)
        <div className={`table ${className}`}>
          <div
            className="table-header"
            style={gridTemplate ? { gridTemplateColumns: gridTemplate } : {}}
          >
            {columns.map((column) => (
              <div
                key={column.key}
                className={`table-cell ${column.className || ""} ${column.sortable ? 'sortable' : ''}`}
                onClick={() => column.sortable && handleSort(column.key)}
                style={{ cursor: column.sortable ? 'pointer' : 'default' }}
              >
                {column.label}
                {column.sortable && (
                  <span className="sort-indicator">
                    {getSortIcon(column.key) || '↕'}
                  </span>
                )}
              </div>
            ))}
          </div>
          {loading.isLoading ? (
            <div className="table-row empty-row">
              <div className="table-cell full-span empty-message">
                {loading.message}
              </div>
            </div>
          ) : data.length > 0 ? (
            data.map((item, index) => (
              <div
                key={item.id || index}
                className={`table-row ${onRowClick ? 'clickable' : ''}`}
                style={
                  gridTemplate ? { gridTemplateColumns: gridTemplate } : {}
                }
                onClick={() => handleRowClick(item)}
              >
                {renderRow
                  ? renderRow(item, index)
                  : columns.map((column) => (
                      <div
                        key={column.key}
                        className={`table-cell ${column.className || ""}`}
                      >
                        {column.render
                          ? column.render(item, index)
                          : item[column.key]}
                      </div>
                    ))}
              </div>
            ))
          ) : (
            <div className="table-row empty-row">
              <div className="table-cell full-span empty-message">
                {emptyMessage}
              </div>
            </div>
          )}
        </div>
      ) : (
        // Traditional table layout
        <table className={`table ${className}`}>
          <thead>
            <tr>
              {columns.map((column) => (
                <th
                  key={column.key}
                  className={`${column.className || ""} ${column.sortable ? 'sortable' : ''}`}
                  onClick={() => column.sortable && handleSort(column.key)}
                  style={{ cursor: column.sortable ? 'pointer' : 'default' }}
                >
                  {column.label}
                  {column.sortable && (
                    <span className="sort-indicator">
                      {getSortIcon(column.key) || '↕'}
                    </span>
                  )}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {loading.isLoading ? (
              <tr>
                <td colSpan={columns.length} className="empty-message">
                  {loading.message}
                </td>
              </tr>
            ) : data.length > 0 ? (
              data.map((item, index) => (
                <tr
                  key={item.id || index}
                  onClick={() => handleRowClick(item)}
                  className={onRowClick ? "clickable" : ""}
                >
                  {renderRow
                    ? renderRow(item, index)
                    : columns.map((column) => (
                        <td key={column.key} className={column.className || ""}>
                          {column.render
                            ? column.render(item, index)
                            : item[column.key]}
                        </td>
                      ))}
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={columns.length} className="empty-message">
                  {emptyMessage}
                </td>
              </tr>
            )}
          </tbody>
        </table>
      )}
    </div>
  );
};

export default Table;
