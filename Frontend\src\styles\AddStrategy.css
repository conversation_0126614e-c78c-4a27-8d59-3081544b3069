/* AddStrategy Component Styles */
.AddStrategy {
  width: 100%;
  max-width: 100%;
}

/* Form Section */
.AddStrategy .AddStrategy__form {
  display: flex;
  flex-direction: column;
  gap: var(--heading5);
}

.AddStrategy .AddStrategy__field {
  display: flex;
  flex-direction: column;
  gap: var(--smallfont);
}
.AddStrategy .uplodeditcss {
  border: 2px dashed var(--light-gray);
  border-radius: var(--border-radius);
  padding: var(--heading4);
  text-align: center;
  background-color: var(--bg-gray);
  transition: all 0.3s ease;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-end;
}
.AddStrategy .AddStrategy__label {
  font-size: var(--basefont);
  font-weight: 600;

  color: var(--secondary-color);
}
.AddStrategy .AddStrategy__checkbox-label {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 5px;
}
/* Input Styles */
.AddStrategy .AddStrategy__input,
.AddStrategy .AddStrategy__select,
.AddStrategy .AddStrategy__textarea {
  padding: var(--smallfont) var(--basefont);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--basefont);
  color: var(--text-color);
  background-color: var(--white);
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
  outline: none;
  font-family: inherit;
}

.AddStrategy .AddStrategy__input:focus,
.AddStrategy .AddStrategy__select:focus,
.AddStrategy .AddStrategy__textarea:focus {
  border-color: var(--btn-color);
  box-shadow: 0 0 0 2px rgba(238, 52, 37, 0.1);
}

.AddStrategy .AddStrategy__input::placeholder,
.AddStrategy .AddStrategy__textarea::placeholder {
  color: var(--dark-gray);
  opacity: 0.7;
}

.AddStrategy .AddStrategy__textarea {
  min-height: 120px;
  resize: vertical;
  line-height: 1.5;
}

.AddStrategy .AddStrategy__select {
  cursor: pointer;
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right var(--smallfont) center;
  background-size: 16px;
  padding-right: var(--heading4);
}

/* Auction Section Styles */
.AddStrategy__auction-section {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: var(--border-radius);
  padding: var(--heading5);
  margin: var(--basefont) 0;
  display: grid;
  gap: 20px;
}

.AddStrategy__section-title {
  font-size: var(--heading6);
  font-weight: 600;
  color: var(--secondary-color);

  padding-bottom: var(--smallfont);
  border-bottom: 1px solid #e9ecef;
}

/* Auction Note Styles */
.AddStrategy__auction-note {
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: var(--border-radius);
  padding: var(--basefont);
  margin-top: var(--basefont);
}

.AddStrategy__auction-note p {
  margin: 0;
  font-size: var(--smallfont);
  color: #856404;
  line-height: 1.4;
}

.AddStrategy__auction-note strong {
  font-weight: 600;
  color: #725502;
}

/* Array Field Styles */
.AddStrategy__array-field {
  display: flex;
  flex-direction: column;
  gap: var(--smallfont);
}

.AddStrategy__array-input {
  display: flex;
  gap: var(--smallfont);
  align-items: center;
}

.AddStrategy__array-input .AddStrategy__input {
  flex: 1;
}

.AddStrategy__array-items {
  display: flex;
  flex-wrap: wrap;
  gap: var(--extrasmallfont);
  margin-top: var(--extrasmallfont);
}

.AddStrategy__array-item {
  display: flex;
  align-items: center;
  gap: var(--extrasmallfont);
  background-color: var(--bg-gray);
  padding: 0 var(--smallfont);
  border-radius: 30px;
  font-size: var(--smallfont);
  color: var(--text-color);
  border: 1px solid var(--light-gray);
}

.AddStrategy__array-item span {
  flex: 1;
}

.AddStrategy__remove-btn {
  background: none;
  border: none;
  color: var(--btn-color);
  cursor: pointer;
  padding: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.3s ease;
}

.AddStrategy__remove-btn:hover {
  background-color: rgba(238, 52, 37, 0.1);
}

/* Summernote Editor Wrapper */
.summernote-wrapper {
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  background-color: var(--white);
  font-family: var(--font-family);
  overflow: hidden;
}

.summernote-wrapper:focus-within {
  border-color: var(--btn-color);
  box-shadow: 0 0 0 2px rgba(238, 52, 37, 0.1);
}

/* Summernote Toolbar Styling */
.summernote-wrapper .note-toolbar {
  background-color: var(--bg-gray);
  border-bottom: 1px solid var(--light-gray);
  padding: var(--extrasmallfont) var(--smallfont);
  border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.summernote-wrapper .note-toolbar .note-btn-group {
  margin-right: var(--extrasmallfont);
}

.summernote-wrapper .note-toolbar .note-btn {
  background-color: transparent;
  border: 1px solid transparent;
  border-radius: var(--border-radius);
  padding: 6px 8px;
  margin: 2px;
  color: var(--text-color);
  font-size: var(--smallfont);
  transition: all 0.2s ease;
}

.summernote-wrapper .note-toolbar .note-btn:hover {
  background-color: var(--white);
  border-color: var(--light-gray);
  color: var(--btn-color);
}

.summernote-wrapper .note-toolbar .note-btn.active {
  background-color: var(--btn-color);
  border-color: var(--btn-color);
  color: var(--white);
}

/* Summernote Editor Area */
.summernote-wrapper .note-editable {
  background-color: var(--white);
  color: var(--text-color);
  font-family: var(--font-family);
  font-size: var(--basefont);
  line-height: 1.6;
  padding: var(--smallfont) var(--basefont);
  border: none;
  outline: none;
}

.summernote-wrapper .note-editable:focus {
  outline: none;
  box-shadow: none;
}

/* Summernote Status Bar */
.summernote-wrapper .note-statusbar {
  background-color: var(--bg-gray);
  border-top: 1px solid var(--light-gray);
  border-radius: 0 0 var(--border-radius) var(--border-radius);
}

/* Legacy rich-text-editor styles for backward compatibility */
.rich-text-editor {
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  background-color: var(--white);
  font-family: var(--font-family);
  overflow: hidden;
}

.rich-text-editor.focused {
  border-color: var(--btn-color);
  box-shadow: 0 0 0 2px rgba(238, 52, 37, 0.1);
}

.rich-text-toolbar {
  background-color: var(--bg-gray);
  border-bottom: 1px solid var(--light-gray);
  padding: var(--extrasmallfont) var(--smallfont);
  display: flex;
  gap: var(--extrasmallfont);
  flex-wrap: wrap;
}

.toolbar-btn {
  background-color: transparent;
  border: 1px solid transparent;
  color: var(--text-color);
  padding: 6px 8px;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  font-size: var(--smallfont);
}

.toolbar-btn:hover {
  background-color: var(--btn-color);
  color: var(--white);
  border-color: var(--btn-color);
}

.toolbar-btn:active {
  transform: translateY(1px);
}

.toolbar-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.toolbar-btn:disabled:hover {
  background-color: transparent;
  color: var(--text-color);
  border-color: transparent;
}

.rich-text-area {
  width: 100%;
  border: none;
  outline: none;
  padding: var(--smallfont) var(--basefont);
  font-size: var(--basefont);
  line-height: 1.6;
  color: var(--text-color);
  font-family: var(--font-family);
  resize: vertical;
  min-height: 150px;
  background-color: var(--white);
}

.rich-text-area::placeholder {
  color: var(--placeholder-color);
  opacity: 0.7;
}

.rich-text-area:focus {
  outline: none;
}

/* Error Display */
.AddStrategy__error {
  background-color: rgba(238, 52, 37, 0.1);
  border: 1px solid var(--btn-color);
  border-radius: var(--border-radius);
  padding: var(--smallfont) var(--basefont);
  margin: var(--smallfont) 0;
}

.AddStrategy__error p {
  color: var(--btn-color);
  margin: 0;
  font-size: var(--smallfont);
  font-weight: 500;
}

/* Loading and Error States */
.AddStrategy .loading-container,
.AddStrategy .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.AddStrategy .loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--light-gray);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}
.wordlable {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.AddStrategy .loading-container p,
.AddStrategy .error-container p {
  color: var(--text-color);
  font-size: var(--basefont);
  margin: 0 0 16px 0;
}

.AddStrategy .error-container h3 {
  color: var(--text-color);
  font-size: var(--heading5);
  margin: 0 0 12px 0;
  font-weight: 600;
}

.AddStrategy .btn {
  padding: 12px 24px;
  border-radius: var(--border-radius);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  font-size: var(--basefont);
  text-decoration: none;
  display: inline-block;
}

/* Header Styles for Edit Strategy */
.AddStrategy__header {
  margin-bottom: var(--heading6);
}

.AddStrategy__title {
  font-size: var(--heading4);
  color: var(--text-color);
  font-weight: 600;
  margin: 0 0 var(--smallfont) 0;
}

.AddStrategy__subtitle {
  font-size: var(--basefont);
  color: var(--dark-gray);
  margin: 0;
}

/* Upload Section */
.AddStrategy__upload {
  border: 2px dashed var(--light-gray);
  border-radius: var(--border-radius);
  padding: var(--heading4);
  text-align: center;
  background-color: var(--bg-gray);
  transition: all 0.3s ease;
  cursor: pointer;
  display: flex;
  justify-content: center;
  flex-direction: column-reverse;
  align-items: center;

  gap: 1rem;
}

.AddStrategy__upload:hover {
  border-color: var(--btn-color);
  background-color: rgba(238, 52, 37, 0.05);
}

.AddStrategy__upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--smallfont);
}

.AddStrategy__upload-content--disabled {
  cursor: not-allowed;
  opacity: 0.6;
  border-color: var(--dark-gray);
}

.AddStrategy__upload-content--disabled:hover {
  border-color: var(--dark-gray);
}

.AddStrategy__upload-icon {
  font-size: var(--heading3);
  color: var(--dark-gray);
  margin-bottom: var(--smallfont);
}

.AddStrategy__upload-text {
  font-size: var(--basefont);
  font-weight: 500;
  color: var(--text-color);
  margin: 0;
}

.AddStrategy__upload-subtext {
  font-size: var(--smallfont);
  color: var(--dark-gray);
  margin: 0;
}

.AddStrategy__upload-guidelines {
  margin-top: 8px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.AddStrategy__upload-guidelines h4 {
  color: #495057;
  font-size: 14px;
  margin-bottom: 8px;
  font-weight: 600;
}

.AddStrategy__upload-guidelines ul {
  list-style: none;
  padding-left: 0;
  margin: 0;
}

.AddStrategy__upload-guidelines li {
  color: #6c757d;
  font-size: 13px;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
}

.AddStrategy__upload-guidelines li:last-child {
  margin-bottom: 0;
}

.AddStrategy__upload-guidelines li svg {
  margin-right: 8px;
  color: #0d6efd;
  flex-shrink: 0;
}

.AddStrategy__upload-guidelines--error {
  background-color: #fff5f5;
  border-color: #ffe3e3;
}

.AddStrategy__upload-guidelines--error h4 {
  color: #e03131;
}

.AddStrategy__upload-guidelines--error li {
  color: #e03131;
}

.AddStrategy__upload-guidelines--error li svg {
  color: #e03131;
}

/* Action Buttons */
.AddStrategy__actions {
  display: flex;
  gap: var(--basefont);
  justify-content: flex-start;
  margin-top: var(--heading5);
  padding-top: var(--heading5);
  border-top: 1px solid var(--light-gray);
}

/* Responsive Design */
@media (max-width: 768px) {
  .AddStrategy__form {
    gap: var(--basefont);
  }

  .AddStrategy__upload {
    padding: var(--basefont);
  }

  .AddStrategy__upload-icon {
    font-size: var(--heading4);
  }

  .AddStrategy__array-items {
    gap: var(--smallfont);
  }

  .AddStrategy__array-item {
    padding: 0px var(--smallfont);
  }

  /* Rich Text Editor responsive styles */
  .rich-text-toolbar {
    padding: var(--smallfont);
    gap: 4px;
  }

  .toolbar-btn {
    padding: 8px 10px;
    font-size: var(--extrasmallfont);
  }

  .rich-text-area {
    padding: var(--basefont);
    font-size: var(--smallfont);
  }
}

@media (max-width: 480px) {
  .AddStrategy__label {
    font-size: var(--smallfont);
  }

  .AddStrategy__input,
  .AddStrategy__select,
  .AddStrategy__textarea {
    padding: var(--extrasmallfont) var(--smallfont);
    font-size: var(--smallfont);
  }

  .AddStrategy__upload {
    padding: var(--smallfont);
  }

  .AddStrategy__upload-text {
    font-size: var(--smallfont);
  }

  .AddStrategy__upload-subtext {
    font-size: var(--extrasmallfont);
  }
  .wordlable {
    display: grid;
    justify-content: space-between;
    align-items: center;
  }
  .AddStrategy__array-input {
    flex-direction: column;
    align-items: stretch;
  }
  .AddStrategy__actions {
    flex-direction: column;
    gap: var(--smallfont);
  }
}

/* Thumbnail Preview */
.AddStrategy .AddStrategy__thumbnail-preview {
  margin-top: var(--basefont);
  border-radius: var(--border-radius);
  overflow: hidden;
  max-width: 80px;
}

.AddStrategy .AddStrategy__thumbnail-preview img {
  width: 100%;
  height: auto;
  display: block;
}

/* File Preview Section */
.AddStrategy__file-preview {
  margin-top: var(--heading5);
  padding: var(--basefont);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background-color: var(--white);
}

.AddStrategy__preview-title {
  font-size: var(--heading6);
  font-weight: 600;
  color: var(--secondary-color);
  margin: 0 0 var(--basefont) 0;
}

.AddStrategy__video-preview,
.AddStrategy__pdf-preview,
.AddStrategy__document-preview,
.AddStrategy__audio-preview,
.AddStrategy__image-preview {
  margin-bottom: var(--basefont);
  border-radius: var(--border-radius);
  overflow: hidden;
}

.AddStrategy__video-element,
.AddStrategy__audio-element {
  border-radius: var(--border-radius);
}

.AddStrategy__preview-actions {
  display: flex;
  gap: var(--smallfont);
  justify-content: flex-start;
}

.AddStrategy__preview-btn {
  display: flex;
  align-items: center;
  gap: var(--extrasmallfont);
  padding: var(--extrasmallfont) var(--smallfont);
  background-color: var(--btn-color);
  color: var(--white);
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.AddStrategy__preview-btn:hover {
  background-color: var(--primary-color);
}

.AddStrategy__file-info {
  margin-top: var(--smallfont);
  padding: var(--smallfont);
  background-color: var(--light-gray);
  border-radius: var(--border-radius);
}

.AddStrategy__file-name {
  font-size: var(--basefont);
  font-weight: 500;
  color: var(--text-color);
  margin: 0 0 var(--extrasmallfont) 0;
  word-break: break-all;
}

.AddStrategy__file-size {
  font-size: var(--smallfont);
  color: var(--dark-gray);
  margin: 0;
}

/* Validation Error Styles */

.AddStrategy .AddStrategy__error-message {
  color: #dc2626;
  font-size: var(--smallfont);
  font-weight: 500;
  margin: 0;
  line-height: 1.4;
}

/* Error container styles for edit page */
.error-container {
  text-align: center;
  padding: 2rem;
  max-width: 500px;
  margin: 2rem auto;
}

.error-container h3 {
  color: var(--btn-color);
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

.error-container p {
  color: var(--text-secondary);
  margin-bottom: 2rem;
  line-height: 1.6;
}

.error-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.error-actions .btn {
  min-width: 120px;
}

/* Payment Setup Required Styles */
.payment-setup-required {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 80vh;
  padding: 2rem;
}

.payment-setup-card {
  background: white;
  border-radius: 16px;
  padding: 3rem;
  max-width: 500px;
  width: 100%;
  text-align: center;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  border: 2px solid #fed7d7;
}

.payment-setup-icon {
  font-size: 4rem;
  margin-bottom: 1.5rem;
}

.payment-setup-card h2 {
  color: #c53030;
  font-size: 1.8rem;
  font-weight: 700;
  margin: 0 0 1rem 0;
}

.payment-setup-card p {
  color: #4a5568;
  font-size: 1.1rem;
  line-height: 1.6;
  margin: 0 0 2rem 0;
}

.payment-setup-benefits {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin: 2rem 0;
  text-align: left;
}

.payment-setup-benefits .benefit-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: #f7fafc;
  border-radius: 8px;
}

.payment-setup-benefits .benefit-icon {
  color: #48bb78;
  font-weight: bold;
  flex-shrink: 0;
}

.payment-setup-benefits span:last-child {
  color: #2d3748;
  font-weight: 500;
}

.payment-setup-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
}

.payment-setup-actions .btn {
  padding: 0.875rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  text-decoration: none;
  border: none;
  font-size: 1rem;
}

.payment-setup-actions .btn-outline {
  background: transparent;
  color: #4a5568;
  border: 2px solid #e2e8f0;
}

.payment-setup-actions .btn-outline:hover {
  background: #f7fafc;
  border-color: #cbd5e0;
}

@media (max-width: 768px) {
  .payment-setup-card {
    padding: 2rem;
    margin: 1rem;
  }

  .payment-setup-actions {
    flex-direction: column;
  }

  .payment-setup-actions .btn {
    width: 100%;
  }
}

.AddStrategy__format-note {
  font-size: 12px;
  color: #6c757d;
  margin-top: 4px;
  margin-bottom: 8px;
}

.AddStrategy__format-note span {
  color: #495057;
  font-weight: 500;
}

/* Preview Button Styles */
.AddStrategy__preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--basefont);
  gap: var(--basefont);
}

.AddStrategy__previewBtn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  font-size: var(--smallfont);
  white-space: nowrap;
  flex-shrink: 0;
}

.AddStrategy__previewBtn:hover {
  transform: translateY(-1px);
}

/* Responsive adjustments for preview button */
@media (max-width: 768px) {
  .AddStrategy__preview-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .AddStrategy__previewBtn {
    align-self: flex-end;
    font-size: var(--extrasmallfont);
    padding: 6px 12px;
  }
}

@media (max-width: 480px) {
  .AddStrategy__preview-header {
    gap: 6px;
  }

  .AddStrategy__previewBtn {
    width: 100%;
    justify-content: center;
  }
}
