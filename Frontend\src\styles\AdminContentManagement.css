/* AdminContentManagement Component Styles */
.AdminContentManagement {
  display: flex;
  flex-direction: column;
  gap: 24px;

  min-height: 100vh;
}

/* Header Section */
.AdminContentManagement .AdminContentManagement__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;

  border-radius: var(--border-radius);

  color: white;
  position: relative;
  overflow: hidden;
}

.AdminContentManagement .AdminContentManagement__header::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
}

.AdminContentManagement .header-left {
  flex: 1;
  max-width: 500px;
  position: relative;
  z-index: 1;
}

.AdminContentManagement .header-title {
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 8px;
  color: white;
}

.AdminContentManagement .header-subtitle {
  font-size: 16px;
  opacity: 0.9;
  margin-bottom: 20px;
}

.AdminContentManagement .search-container {
  position: relative;
  display: flex;
  align-items: center;
}

.AdminContentManagement .search-icon {
  position: absolute;
  left: 16px;
  color: #64748b;
  font-size: 16px;
  z-index: 2;
}

.AdminContentManagement .search-input {
  width: 100%;
  padding: 14px 16px 14px 48px;
border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: 12px;
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;


}

.AdminContentManagement .search-input:focus {
  outline: none;
  background-color: white;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.AdminContentManagement .search-input::placeholder {
  color: #64748b;
}

.AdminContentManagement .header-right {
  display: flex;
  gap: 12px;
  position: relative;
  z-index: 1;
}

/* Modern Button Styles */
.AdminContentManagement .btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border: none;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-decoration: none;
  position: relative;
  overflow: hidden;
  white-space: nowrap;
}

.AdminContentManagement .btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.AdminContentManagement .btn:hover::before {
  left: 100%;
}

.AdminContentManagement .btn.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.AdminContentManagement .btn.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.AdminContentManagement .btn.btn-outline {
  background-color: white;
  color: #64748b;
  border: 2px solid #e2e8f0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.AdminContentManagement .btn.btn-outline:hover {
  background-color: #f8fafc;
  border-color: #cbd5e1;
  transform: translateY(-1px);
}

.AdminContentManagement .btn.btn-success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.AdminContentManagement .btn.btn-success:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
}

.AdminContentManagement .btn.btn-warning {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
}

.AdminContentManagement .btn.btn-warning:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);
}

.AdminContentManagement .btn.btn-danger {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
}

.AdminContentManagement .btn.btn-danger:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
}

/* Modern Filters Section */
.AdminContentManagement .AdminContentManagement__filters {
  display: flex;
  align-items: center;
  gap: 20px;

  background: white;

  flex-wrap: wrap;
}

.AdminContentManagement .filter-group {
  display: flex;
  align-items: center;
  gap: 12px;
  position: relative;
}

.AdminContentManagement .filter-icon {
  color: #64748b;
  font-size: 18px;
}

.AdminContentManagement .filter-select {
  padding: 12px 16px;
  border: 1px solid #e2e8f0;
  border-radius: 10px;
  font-size: 14px;
  background-color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 150px;
  font-weight: 500;
}

.AdminContentManagement .filter-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.AdminContentManagement .filter-select:hover {
  border-color: #cbd5e1;
}

.AdminContentManagement .bulk-actions {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-left: auto;
  padding: 16px 20px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.AdminContentManagement .selected-count {
  font-size: 14px;
  color: #475569;
  font-weight: 600;
  padding: 8px 12px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

/* Modern Table Section */
.AdminContentManagement .AdminContentManagement__table {
  background: white;
  border-radius: var(--border-radius);

  overflow: hidden;
}

.AdminContentManagement .table-container {
  overflow-x: auto;
  border-radius: var(--border-radius);
}

.AdminContentManagement .content-table {
  width: 100%;
  border-collapse: collapse;
}

.AdminContentManagement .content-table th,
.AdminContentManagement .content-table td {
  padding: 16px 20px;
  text-align: left;
  border-bottom: 1px solid #f1f5f9;
  vertical-align: middle;
  max-width: 200px;
}

.AdminContentManagement .content-table th {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  font-weight: 700;
  font-size: 12px;
  color: #475569;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: sticky;
  top: 0;
  z-index: 10;
}

.AdminContentManagement .content-table td {
  font-size: 14px;
  color: #334155;
}

.AdminContentManagement .content-table tr {
  transition: all 0.2s ease;
}

.AdminContentManagement .content-table tr:hover {
  background-color: #f8fafc;
  transform: scale(1.001);
}

/* Modern Status Controls */
.AdminContentManagement .status-controls {
  display: grid;
  grid-template-columns: 90px 1fr;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
  max-width: fit-content;
}

.AdminContentManagement .status-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
text-align: center;
  letter-spacing: 0.5px;
 
  transition: all 0.3s ease;
}

.AdminContentManagement .status-published {
  background-color: #dcfce7;
  color:#166534;
  
  
}

.AdminContentManagement .status-draft {
    background-color: #f3f4f6;
    color: #6b7280;
}

.AdminContentManagement .status-sold {
  background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(6, 182, 212, 0.3);
}

/* Modern Toggle Button */
.AdminContentManagement .btn-action.toggle {
  background: none;
  border: none;

  cursor: pointer;
  font-size: 24px;
  color: #10b981;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  border-radius: 8px;
}

.AdminContentManagement .btn-action.toggle:hover {
  color: #059669;

  transform: scale(1.1);
}

.AdminContentManagement .btn-action.toggle:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Content Info */
.AdminContentManagement .content-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.AdminContentManagement .content-thumbnail {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;

  border-radius: 4px;
  overflow: hidden;
  flex-shrink: 0;
}

.AdminContentManagement .content-thumbnail img {
  width: 32px;
  height: 32px;
  object-fit: cover;
  object-position: center;
  border-radius: 4px;
}

.AdminContentManagement .content-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}
.AdminContentManagement .content-item {
  display: flex;
  align-items: center;
  gap: 12px;

  overflow: hidden;
}
.AdminContentManagement .content-image img {
  width: 32px;
  height: 32px;
  object-fit: cover;
  object-position: center;
  border-radius: 4px;
}
.AdminContentManagement .content-title {
  font-weight: 500;
  color: #212529;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 200px;
  overflow: hidden;
}

.AdminContentManagement .content-category {
  font-size: 12px;
  color: #6c757d;
}

/* Table Actions */
.AdminContentManagement .table-actions {
  display: flex;
  gap: var(--smallfont);
  align-items: center;
  justify-content: center;
}

.AdminContentManagement .btn-action {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.AdminContentManagement .btn-action.edit {
  background-color: var(--bg-gray);
  color: var(--secondary-color);
}

.AdminContentManagement .btn-action.edit:hover {
  background-color: var(--light-gray);
  transform: scale(1.05);
}

.AdminContentManagement .btn-action.delete {
  background-color: #fef2f2;
  color: #ef4444;
}

.AdminContentManagement .btn-action.delete:hover {
  background-color: #fee2e2;
  transform: scale(1.05);
}

/* Tooltip for action buttons */
.AdminContentManagement .btn-action::before {
  content: attr(title);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background-color: var(--secondary-color);
  color: var(--white);
  padding: 4px 8px;
  border-radius: var(--border-radius);
  font-size: var(--extrasmallfont);
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  z-index: var(--z-index-tooltip);
  pointer-events: none;
}

.AdminContentManagement .btn-action:hover::before {
  opacity: 1;
  visibility: visible;
  bottom: calc(100% + 8px);
}

/* No Results */
.AdminContentManagement .no-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--heading3);
  text-align: center;
}

.AdminContentManagement .no-results-icon {
  font-size: var(--heading2);
  color: var(--light-gray);
  margin-bottom: var(--basefont);
}

.AdminContentManagement .no-results h3 {
  margin: 0 0 var(--smallfont) 0;
  color: var(--secondary-color);
}

.AdminContentManagement .no-results p {
  margin: 0;
  color: var(--dark-gray);
  font-size: var(--smallfont);
}

/* Pagination */
.AdminContentManagement .AdminContentManagement__pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--basefont);
  background-color: var(--white);
  border-radius: var(--border-radius);
}

.AdminContentManagement .pagination-info {
  font-size: var(--smallfont);
  color: var(--dark-gray);
}

.AdminContentManagement .pagination-controls {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
}

.AdminContentManagement .page-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.AdminContentManagement .page-number.active {
  background-color: var(--btn-color);
  color: var(--white);
}

/* Responsive styles */
@media (max-width: 1024px) {
  .AdminContentManagement .AdminContentManagement__header {
    gap: var(--basefont);
  }

  .AdminContentManagement .header-left {
    max-width: 350px;
  }

  .AdminContentManagement .content-table {
    font-size: var(--smallfont);
  }

  .AdminContentManagement .content-table th,
  .AdminContentManagement .content-table td {
    padding: var(--smallfont) 8px;
  }
}

@media (max-width: 768px) {
  .AdminContentManagement .AdminContentManagement__header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--smallfont);
  }

  .AdminContentManagement .header-left {
    max-width: none;
    min-width: auto;
  }

  .AdminContentManagement .header-right {
    justify-content: flex-start;
    flex-wrap: wrap;
  }

  .AdminContentManagement .AdminContentManagement__filters {
    flex-wrap: wrap;
    gap: var(--smallfont);
    padding: var(--smallfont);
  }

  .AdminContentManagement .filter-group {
    flex: 1;
    min-width: 120px;
  }

  .AdminContentManagement .bulk-actions {
    margin-left: 0;
    margin-top: var(--smallfont);
    width: 100%;
    justify-content: space-between;
  }

  .AdminContentManagement .table-container {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .AdminContentManagement .content-table {
    font-size: var(--extrasmallfont);
    min-width: 600px;
  }

  .AdminContentManagement .content-table th,
  .AdminContentManagement .content-table td {
    padding: var(--smallfont) 6px;
  }

  .AdminContentManagement .content-thumbnail {
    width: 28px;
    height: 28px;
    font-size: var(--smallfont);
  }

  .AdminContentManagement .content-thumbnail img {
    width: 28px;
    height: 28px;
  }

  .AdminContentManagement .content-title {
    font-size: var(--extrasmallfont);
  }

  .AdminContentManagement .table-actions {
    gap: 4px;
  }

  .AdminContentManagement .btn-action {
    width: 28px;
    height: 28px;
    font-size: var(--extrasmallfont);
  }

  .AdminContentManagement .AdminContentManagement__pagination {
    flex-direction: column;
    gap: var(--smallfont);
    align-items: center;
  }

  .AdminContentManagement .pagination-controls {
    order: -1;
  }
}

@media (max-width: 480px) {
  .AdminContentManagement {
    gap: var(--smallfont);
  }

  .AdminContentManagement .AdminContentManagement__header {
    gap: 8px;
  }

  .AdminContentManagement .header-right {
    flex-direction: column;
    gap: 8px;
  }

  .AdminContentManagement .btn {
    width: 100%;
    justify-content: center;
  }

  .AdminContentManagement .AdminContentManagement__filters {
    flex-direction: column;
    align-items: stretch;
  }

  .AdminContentManagement .filter-group {
    width: 100%;
    justify-content: space-between;
  }

  .AdminContentManagement .bulk-actions {
    flex-direction: column;
    gap: 8px;
  }

  .AdminContentManagement .content-table {
    min-width: 500px;
  }

  .AdminContentManagement .table-actions {
    flex-direction: column;
    gap: 2px;
  }

  .AdminContentManagement .btn-action {
    width: 24px;
    height: 24px;
    font-size: 10px;
  }

  /* Hide tooltips on mobile */
  .AdminContentManagement .btn-action::before {
    display: none;
  }

  .AdminContentManagement .content-thumbnail {
    width: 24px;
    height: 24px;
  }

  .AdminContentManagement .content-thumbnail img {
    width: 24px;
    height: 24px;
  }
}

/* Extra small mobile devices */
@media (max-width: 360px) {
  .AdminContentManagement .AdminContentManagement__table {
    margin: 0 -8px;
  }

  .AdminContentManagement .table-container {
    border-radius: 0;
  }

  .AdminContentManagement .content-table {
    min-width: 450px;
  }

  .AdminContentManagement .content-info {
    gap: 6px;
  }

  .AdminContentManagement .content-thumbnail {
    width: 20px;
    height: 20px;
  }
}
