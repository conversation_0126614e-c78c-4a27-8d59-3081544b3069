.BuyerDownloads {
  display: flex;
  flex-direction: column;
  width: 100%;
}

/* Table Styles - Following BuyerAccountDashboard pattern */
.BuyerDownloads .table {
  width: 100%;
  border-collapse: collapse;
  font-size: var(--smallfont);
  background-color: var(--white);
  border-radius: var(--border-radius-large);
  overflow: hidden;
  overflow-x: auto;
}

.BuyerDownloads .table th,
.BuyerDownloads .table td {
  padding: 12px 10px;
  text-align: left;
  border-bottom: 1px solid var(--light-gray);
  vertical-align: middle;
}

.BuyerDownloads .table th {
  background-color: var(--bg-gray);
  font-weight: 600;
  color: var(--secondary-color);
  font-size: var(--extrasmallfont);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.BuyerDownloads .table tr:last-child td {
  border-bottom: none;
}

/* Remove old grid-based table-row/table-header/table-cell styles */
.content-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.BuyerDownloads .content-image {
  width: 50px;
  height: 50px;
  border-radius: var(--border-radius);
  overflow: hidden;
  flex-shrink: 0;
}

.BuyerDownloads .content-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.BuyerDownloads .content-info {
  display: flex;
  flex-direction: column;
  text-align: left; /* Keep text left-aligned within the centered container */
}

.BuyerDownloads .content-title {
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

.BuyerDownloads .content-coach {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
}

.BuyerDownloads .content-type {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
  margin-top: 2px;
}

.BuyerDownloads .status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: var(--border-radius);
  font-size: var(--extrasmallfont);
  font-weight: 500;
  text-align: center;
}

.BuyerDownloads .status-badge.downloaded {
  background-color: rgba(46, 204, 113, 0.1);
  color: #2ecc71;
}

.BuyerDownloads .status-badge.pending {
  background-color: rgba(243, 156, 18, 0.1);
  color: #f39c12;
}

/* Action buttons styling */
.BuyerDownloads .action-buttons {
  display: flex;
  gap: 0.5rem;
  align-items: center;
  justify-content: center;
}

.BuyerDownloads .action-btn {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
}

.BuyerDownloads .download-btn {
  background-color: var(--second-primary-color);
  color: white;
}

.BuyerDownloads .download-btn:hover:not(:disabled) {
  background-color: var(--second-primary-color);
  transform: translateY(-1px);
}

.BuyerDownloads .download-btn:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

.BuyerDownloads .view-btn {
  background-color: white;
  color: rgb(0, 0, 0);
  font-size: var(--heading6);
}

.BuyerDownloads .view-btn:hover {
  transform: scale(1.02);
}

/* Spinner for loading state */
.BuyerDownloads .spinner {
  width: 14px;
  height: 14px;
  border: 2px solid #ffffff;
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}
.BuyerDownloads .table-cells {
  font-size: var(--smallfont);
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.BuyerDownloads__empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px 20px;
  text-align: center;
  background: var(--light-background);
  border-radius: var(--border-radius-large);
  border: 2px dashed var(--light-gray);
}
.BuyerDownloads__empty h3 {
  color: var(--text-color);
  font-size: var(--heading5);
  margin: 0 0 12px 0;
  font-weight: 600;
}
.BuyerDownloads__empty p {
  font-size: var(--basefont);
}

/* Responsive styles */
@media (max-width: 992px) {
  .BuyerDownloads .table-header,
  .BuyerDownloads .table-row {
    grid-template-columns: 0.5fr 1fr 2fr 1.5fr 1fr 1fr 1fr;
  }

  .BuyerDownloads .content-title {
    max-width: 150px;
  }
}

@media (max-width: 768px) {
  .BuyerDownloads .table {
    overflow-x: auto;
  }

  .BuyerDownloads .table-header,
  .BuyerDownloads .table-row {
    min-width: 700px;
  }
}

.BuyerDownloads__pagination {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.no-data-message {
  text-align: center;
  padding: 2rem;
  color: #666;
  font-size: 1.1rem;
}
