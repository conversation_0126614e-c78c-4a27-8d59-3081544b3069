{"version": 3, "sources": ["../../canvas-confetti/dist/confetti.module.mjs"], "sourcesContent": ["// canvas-confetti v1.9.3 built on 2024-04-30T22:19:17.794Z\nvar module = {};\n\n// source content\n/* globals Map */\n\n(function main(global, module, isWorker, workerSize) {\n  var canUseWorker = !!(\n    global.Worker &&\n    global.Blob &&\n    global.Promise &&\n    global.OffscreenCanvas &&\n    global.OffscreenCanvasRenderingContext2D &&\n    global.HTMLCanvasElement &&\n    global.HTMLCanvasElement.prototype.transferControlToOffscreen &&\n    global.URL &&\n    global.URL.createObjectURL);\n\n  var canUsePaths = typeof Path2D === 'function' && typeof DOMMatrix === 'function';\n  var canDrawBitmap = (function () {\n    // this mostly supports ssr\n    if (!global.OffscreenCanvas) {\n      return false;\n    }\n\n    var canvas = new OffscreenCanvas(1, 1);\n    var ctx = canvas.getContext('2d');\n    ctx.fillRect(0, 0, 1, 1);\n    var bitmap = canvas.transferToImageBitmap();\n\n    try {\n      ctx.createPattern(bitmap, 'no-repeat');\n    } catch (e) {\n      return false;\n    }\n\n    return true;\n  })();\n\n  function noop() {}\n\n  // create a promise if it exists, otherwise, just\n  // call the function directly\n  function promise(func) {\n    var ModulePromise = module.exports.Promise;\n    var Prom = ModulePromise !== void 0 ? ModulePromise : global.Promise;\n\n    if (typeof Prom === 'function') {\n      return new Prom(func);\n    }\n\n    func(noop, noop);\n\n    return null;\n  }\n\n  var bitmapMapper = (function (skipTransform, map) {\n    // see https://github.com/catdad/canvas-confetti/issues/209\n    // creating canvases is actually pretty expensive, so we should create a\n    // 1:1 map for bitmap:canvas, so that we can animate the confetti in\n    // a performant manner, but also not store them forever so that we don't\n    // have a memory leak\n    return {\n      transform: function(bitmap) {\n        if (skipTransform) {\n          return bitmap;\n        }\n\n        if (map.has(bitmap)) {\n          return map.get(bitmap);\n        }\n\n        var canvas = new OffscreenCanvas(bitmap.width, bitmap.height);\n        var ctx = canvas.getContext('2d');\n        ctx.drawImage(bitmap, 0, 0);\n\n        map.set(bitmap, canvas);\n\n        return canvas;\n      },\n      clear: function () {\n        map.clear();\n      }\n    };\n  })(canDrawBitmap, new Map());\n\n  var raf = (function () {\n    var TIME = Math.floor(1000 / 60);\n    var frame, cancel;\n    var frames = {};\n    var lastFrameTime = 0;\n\n    if (typeof requestAnimationFrame === 'function' && typeof cancelAnimationFrame === 'function') {\n      frame = function (cb) {\n        var id = Math.random();\n\n        frames[id] = requestAnimationFrame(function onFrame(time) {\n          if (lastFrameTime === time || lastFrameTime + TIME - 1 < time) {\n            lastFrameTime = time;\n            delete frames[id];\n\n            cb();\n          } else {\n            frames[id] = requestAnimationFrame(onFrame);\n          }\n        });\n\n        return id;\n      };\n      cancel = function (id) {\n        if (frames[id]) {\n          cancelAnimationFrame(frames[id]);\n        }\n      };\n    } else {\n      frame = function (cb) {\n        return setTimeout(cb, TIME);\n      };\n      cancel = function (timer) {\n        return clearTimeout(timer);\n      };\n    }\n\n    return { frame: frame, cancel: cancel };\n  }());\n\n  var getWorker = (function () {\n    var worker;\n    var prom;\n    var resolves = {};\n\n    function decorate(worker) {\n      function execute(options, callback) {\n        worker.postMessage({ options: options || {}, callback: callback });\n      }\n      worker.init = function initWorker(canvas) {\n        var offscreen = canvas.transferControlToOffscreen();\n        worker.postMessage({ canvas: offscreen }, [offscreen]);\n      };\n\n      worker.fire = function fireWorker(options, size, done) {\n        if (prom) {\n          execute(options, null);\n          return prom;\n        }\n\n        var id = Math.random().toString(36).slice(2);\n\n        prom = promise(function (resolve) {\n          function workerDone(msg) {\n            if (msg.data.callback !== id) {\n              return;\n            }\n\n            delete resolves[id];\n            worker.removeEventListener('message', workerDone);\n\n            prom = null;\n\n            bitmapMapper.clear();\n\n            done();\n            resolve();\n          }\n\n          worker.addEventListener('message', workerDone);\n          execute(options, id);\n\n          resolves[id] = workerDone.bind(null, { data: { callback: id }});\n        });\n\n        return prom;\n      };\n\n      worker.reset = function resetWorker() {\n        worker.postMessage({ reset: true });\n\n        for (var id in resolves) {\n          resolves[id]();\n          delete resolves[id];\n        }\n      };\n    }\n\n    return function () {\n      if (worker) {\n        return worker;\n      }\n\n      if (!isWorker && canUseWorker) {\n        var code = [\n          'var CONFETTI, SIZE = {}, module = {};',\n          '(' + main.toString() + ')(this, module, true, SIZE);',\n          'onmessage = function(msg) {',\n          '  if (msg.data.options) {',\n          '    CONFETTI(msg.data.options).then(function () {',\n          '      if (msg.data.callback) {',\n          '        postMessage({ callback: msg.data.callback });',\n          '      }',\n          '    });',\n          '  } else if (msg.data.reset) {',\n          '    CONFETTI && CONFETTI.reset();',\n          '  } else if (msg.data.resize) {',\n          '    SIZE.width = msg.data.resize.width;',\n          '    SIZE.height = msg.data.resize.height;',\n          '  } else if (msg.data.canvas) {',\n          '    SIZE.width = msg.data.canvas.width;',\n          '    SIZE.height = msg.data.canvas.height;',\n          '    CONFETTI = module.exports.create(msg.data.canvas);',\n          '  }',\n          '}',\n        ].join('\\n');\n        try {\n          worker = new Worker(URL.createObjectURL(new Blob([code])));\n        } catch (e) {\n          // eslint-disable-next-line no-console\n          typeof console !== undefined && typeof console.warn === 'function' ? console.warn('🎊 Could not load worker', e) : null;\n\n          return null;\n        }\n\n        decorate(worker);\n      }\n\n      return worker;\n    };\n  })();\n\n  var defaults = {\n    particleCount: 50,\n    angle: 90,\n    spread: 45,\n    startVelocity: 45,\n    decay: 0.9,\n    gravity: 1,\n    drift: 0,\n    ticks: 200,\n    x: 0.5,\n    y: 0.5,\n    shapes: ['square', 'circle'],\n    zIndex: 100,\n    colors: [\n      '#26ccff',\n      '#a25afd',\n      '#ff5e7e',\n      '#88ff5a',\n      '#fcff42',\n      '#ffa62d',\n      '#ff36ff'\n    ],\n    // probably should be true, but back-compat\n    disableForReducedMotion: false,\n    scalar: 1\n  };\n\n  function convert(val, transform) {\n    return transform ? transform(val) : val;\n  }\n\n  function isOk(val) {\n    return !(val === null || val === undefined);\n  }\n\n  function prop(options, name, transform) {\n    return convert(\n      options && isOk(options[name]) ? options[name] : defaults[name],\n      transform\n    );\n  }\n\n  function onlyPositiveInt(number){\n    return number < 0 ? 0 : Math.floor(number);\n  }\n\n  function randomInt(min, max) {\n    // [min, max)\n    return Math.floor(Math.random() * (max - min)) + min;\n  }\n\n  function toDecimal(str) {\n    return parseInt(str, 16);\n  }\n\n  function colorsToRgb(colors) {\n    return colors.map(hexToRgb);\n  }\n\n  function hexToRgb(str) {\n    var val = String(str).replace(/[^0-9a-f]/gi, '');\n\n    if (val.length < 6) {\n        val = val[0]+val[0]+val[1]+val[1]+val[2]+val[2];\n    }\n\n    return {\n      r: toDecimal(val.substring(0,2)),\n      g: toDecimal(val.substring(2,4)),\n      b: toDecimal(val.substring(4,6))\n    };\n  }\n\n  function getOrigin(options) {\n    var origin = prop(options, 'origin', Object);\n    origin.x = prop(origin, 'x', Number);\n    origin.y = prop(origin, 'y', Number);\n\n    return origin;\n  }\n\n  function setCanvasWindowSize(canvas) {\n    canvas.width = document.documentElement.clientWidth;\n    canvas.height = document.documentElement.clientHeight;\n  }\n\n  function setCanvasRectSize(canvas) {\n    var rect = canvas.getBoundingClientRect();\n    canvas.width = rect.width;\n    canvas.height = rect.height;\n  }\n\n  function getCanvas(zIndex) {\n    var canvas = document.createElement('canvas');\n\n    canvas.style.position = 'fixed';\n    canvas.style.top = '0px';\n    canvas.style.left = '0px';\n    canvas.style.pointerEvents = 'none';\n    canvas.style.zIndex = zIndex;\n\n    return canvas;\n  }\n\n  function ellipse(context, x, y, radiusX, radiusY, rotation, startAngle, endAngle, antiClockwise) {\n    context.save();\n    context.translate(x, y);\n    context.rotate(rotation);\n    context.scale(radiusX, radiusY);\n    context.arc(0, 0, 1, startAngle, endAngle, antiClockwise);\n    context.restore();\n  }\n\n  function randomPhysics(opts) {\n    var radAngle = opts.angle * (Math.PI / 180);\n    var radSpread = opts.spread * (Math.PI / 180);\n\n    return {\n      x: opts.x,\n      y: opts.y,\n      wobble: Math.random() * 10,\n      wobbleSpeed: Math.min(0.11, Math.random() * 0.1 + 0.05),\n      velocity: (opts.startVelocity * 0.5) + (Math.random() * opts.startVelocity),\n      angle2D: -radAngle + ((0.5 * radSpread) - (Math.random() * radSpread)),\n      tiltAngle: (Math.random() * (0.75 - 0.25) + 0.25) * Math.PI,\n      color: opts.color,\n      shape: opts.shape,\n      tick: 0,\n      totalTicks: opts.ticks,\n      decay: opts.decay,\n      drift: opts.drift,\n      random: Math.random() + 2,\n      tiltSin: 0,\n      tiltCos: 0,\n      wobbleX: 0,\n      wobbleY: 0,\n      gravity: opts.gravity * 3,\n      ovalScalar: 0.6,\n      scalar: opts.scalar,\n      flat: opts.flat\n    };\n  }\n\n  function updateFetti(context, fetti) {\n    fetti.x += Math.cos(fetti.angle2D) * fetti.velocity + fetti.drift;\n    fetti.y += Math.sin(fetti.angle2D) * fetti.velocity + fetti.gravity;\n    fetti.velocity *= fetti.decay;\n\n    if (fetti.flat) {\n      fetti.wobble = 0;\n      fetti.wobbleX = fetti.x + (10 * fetti.scalar);\n      fetti.wobbleY = fetti.y + (10 * fetti.scalar);\n\n      fetti.tiltSin = 0;\n      fetti.tiltCos = 0;\n      fetti.random = 1;\n    } else {\n      fetti.wobble += fetti.wobbleSpeed;\n      fetti.wobbleX = fetti.x + ((10 * fetti.scalar) * Math.cos(fetti.wobble));\n      fetti.wobbleY = fetti.y + ((10 * fetti.scalar) * Math.sin(fetti.wobble));\n\n      fetti.tiltAngle += 0.1;\n      fetti.tiltSin = Math.sin(fetti.tiltAngle);\n      fetti.tiltCos = Math.cos(fetti.tiltAngle);\n      fetti.random = Math.random() + 2;\n    }\n\n    var progress = (fetti.tick++) / fetti.totalTicks;\n\n    var x1 = fetti.x + (fetti.random * fetti.tiltCos);\n    var y1 = fetti.y + (fetti.random * fetti.tiltSin);\n    var x2 = fetti.wobbleX + (fetti.random * fetti.tiltCos);\n    var y2 = fetti.wobbleY + (fetti.random * fetti.tiltSin);\n\n    context.fillStyle = 'rgba(' + fetti.color.r + ', ' + fetti.color.g + ', ' + fetti.color.b + ', ' + (1 - progress) + ')';\n\n    context.beginPath();\n\n    if (canUsePaths && fetti.shape.type === 'path' && typeof fetti.shape.path === 'string' && Array.isArray(fetti.shape.matrix)) {\n      context.fill(transformPath2D(\n        fetti.shape.path,\n        fetti.shape.matrix,\n        fetti.x,\n        fetti.y,\n        Math.abs(x2 - x1) * 0.1,\n        Math.abs(y2 - y1) * 0.1,\n        Math.PI / 10 * fetti.wobble\n      ));\n    } else if (fetti.shape.type === 'bitmap') {\n      var rotation = Math.PI / 10 * fetti.wobble;\n      var scaleX = Math.abs(x2 - x1) * 0.1;\n      var scaleY = Math.abs(y2 - y1) * 0.1;\n      var width = fetti.shape.bitmap.width * fetti.scalar;\n      var height = fetti.shape.bitmap.height * fetti.scalar;\n\n      var matrix = new DOMMatrix([\n        Math.cos(rotation) * scaleX,\n        Math.sin(rotation) * scaleX,\n        -Math.sin(rotation) * scaleY,\n        Math.cos(rotation) * scaleY,\n        fetti.x,\n        fetti.y\n      ]);\n\n      // apply the transform matrix from the confetti shape\n      matrix.multiplySelf(new DOMMatrix(fetti.shape.matrix));\n\n      var pattern = context.createPattern(bitmapMapper.transform(fetti.shape.bitmap), 'no-repeat');\n      pattern.setTransform(matrix);\n\n      context.globalAlpha = (1 - progress);\n      context.fillStyle = pattern;\n      context.fillRect(\n        fetti.x - (width / 2),\n        fetti.y - (height / 2),\n        width,\n        height\n      );\n      context.globalAlpha = 1;\n    } else if (fetti.shape === 'circle') {\n      context.ellipse ?\n        context.ellipse(fetti.x, fetti.y, Math.abs(x2 - x1) * fetti.ovalScalar, Math.abs(y2 - y1) * fetti.ovalScalar, Math.PI / 10 * fetti.wobble, 0, 2 * Math.PI) :\n        ellipse(context, fetti.x, fetti.y, Math.abs(x2 - x1) * fetti.ovalScalar, Math.abs(y2 - y1) * fetti.ovalScalar, Math.PI / 10 * fetti.wobble, 0, 2 * Math.PI);\n    } else if (fetti.shape === 'star') {\n      var rot = Math.PI / 2 * 3;\n      var innerRadius = 4 * fetti.scalar;\n      var outerRadius = 8 * fetti.scalar;\n      var x = fetti.x;\n      var y = fetti.y;\n      var spikes = 5;\n      var step = Math.PI / spikes;\n\n      while (spikes--) {\n        x = fetti.x + Math.cos(rot) * outerRadius;\n        y = fetti.y + Math.sin(rot) * outerRadius;\n        context.lineTo(x, y);\n        rot += step;\n\n        x = fetti.x + Math.cos(rot) * innerRadius;\n        y = fetti.y + Math.sin(rot) * innerRadius;\n        context.lineTo(x, y);\n        rot += step;\n      }\n    } else {\n      context.moveTo(Math.floor(fetti.x), Math.floor(fetti.y));\n      context.lineTo(Math.floor(fetti.wobbleX), Math.floor(y1));\n      context.lineTo(Math.floor(x2), Math.floor(y2));\n      context.lineTo(Math.floor(x1), Math.floor(fetti.wobbleY));\n    }\n\n    context.closePath();\n    context.fill();\n\n    return fetti.tick < fetti.totalTicks;\n  }\n\n  function animate(canvas, fettis, resizer, size, done) {\n    var animatingFettis = fettis.slice();\n    var context = canvas.getContext('2d');\n    var animationFrame;\n    var destroy;\n\n    var prom = promise(function (resolve) {\n      function onDone() {\n        animationFrame = destroy = null;\n\n        context.clearRect(0, 0, size.width, size.height);\n        bitmapMapper.clear();\n\n        done();\n        resolve();\n      }\n\n      function update() {\n        if (isWorker && !(size.width === workerSize.width && size.height === workerSize.height)) {\n          size.width = canvas.width = workerSize.width;\n          size.height = canvas.height = workerSize.height;\n        }\n\n        if (!size.width && !size.height) {\n          resizer(canvas);\n          size.width = canvas.width;\n          size.height = canvas.height;\n        }\n\n        context.clearRect(0, 0, size.width, size.height);\n\n        animatingFettis = animatingFettis.filter(function (fetti) {\n          return updateFetti(context, fetti);\n        });\n\n        if (animatingFettis.length) {\n          animationFrame = raf.frame(update);\n        } else {\n          onDone();\n        }\n      }\n\n      animationFrame = raf.frame(update);\n      destroy = onDone;\n    });\n\n    return {\n      addFettis: function (fettis) {\n        animatingFettis = animatingFettis.concat(fettis);\n\n        return prom;\n      },\n      canvas: canvas,\n      promise: prom,\n      reset: function () {\n        if (animationFrame) {\n          raf.cancel(animationFrame);\n        }\n\n        if (destroy) {\n          destroy();\n        }\n      }\n    };\n  }\n\n  function confettiCannon(canvas, globalOpts) {\n    var isLibCanvas = !canvas;\n    var allowResize = !!prop(globalOpts || {}, 'resize');\n    var hasResizeEventRegistered = false;\n    var globalDisableForReducedMotion = prop(globalOpts, 'disableForReducedMotion', Boolean);\n    var shouldUseWorker = canUseWorker && !!prop(globalOpts || {}, 'useWorker');\n    var worker = shouldUseWorker ? getWorker() : null;\n    var resizer = isLibCanvas ? setCanvasWindowSize : setCanvasRectSize;\n    var initialized = (canvas && worker) ? !!canvas.__confetti_initialized : false;\n    var preferLessMotion = typeof matchMedia === 'function' && matchMedia('(prefers-reduced-motion)').matches;\n    var animationObj;\n\n    function fireLocal(options, size, done) {\n      var particleCount = prop(options, 'particleCount', onlyPositiveInt);\n      var angle = prop(options, 'angle', Number);\n      var spread = prop(options, 'spread', Number);\n      var startVelocity = prop(options, 'startVelocity', Number);\n      var decay = prop(options, 'decay', Number);\n      var gravity = prop(options, 'gravity', Number);\n      var drift = prop(options, 'drift', Number);\n      var colors = prop(options, 'colors', colorsToRgb);\n      var ticks = prop(options, 'ticks', Number);\n      var shapes = prop(options, 'shapes');\n      var scalar = prop(options, 'scalar');\n      var flat = !!prop(options, 'flat');\n      var origin = getOrigin(options);\n\n      var temp = particleCount;\n      var fettis = [];\n\n      var startX = canvas.width * origin.x;\n      var startY = canvas.height * origin.y;\n\n      while (temp--) {\n        fettis.push(\n          randomPhysics({\n            x: startX,\n            y: startY,\n            angle: angle,\n            spread: spread,\n            startVelocity: startVelocity,\n            color: colors[temp % colors.length],\n            shape: shapes[randomInt(0, shapes.length)],\n            ticks: ticks,\n            decay: decay,\n            gravity: gravity,\n            drift: drift,\n            scalar: scalar,\n            flat: flat\n          })\n        );\n      }\n\n      // if we have a previous canvas already animating,\n      // add to it\n      if (animationObj) {\n        return animationObj.addFettis(fettis);\n      }\n\n      animationObj = animate(canvas, fettis, resizer, size , done);\n\n      return animationObj.promise;\n    }\n\n    function fire(options) {\n      var disableForReducedMotion = globalDisableForReducedMotion || prop(options, 'disableForReducedMotion', Boolean);\n      var zIndex = prop(options, 'zIndex', Number);\n\n      if (disableForReducedMotion && preferLessMotion) {\n        return promise(function (resolve) {\n          resolve();\n        });\n      }\n\n      if (isLibCanvas && animationObj) {\n        // use existing canvas from in-progress animation\n        canvas = animationObj.canvas;\n      } else if (isLibCanvas && !canvas) {\n        // create and initialize a new canvas\n        canvas = getCanvas(zIndex);\n        document.body.appendChild(canvas);\n      }\n\n      if (allowResize && !initialized) {\n        // initialize the size of a user-supplied canvas\n        resizer(canvas);\n      }\n\n      var size = {\n        width: canvas.width,\n        height: canvas.height\n      };\n\n      if (worker && !initialized) {\n        worker.init(canvas);\n      }\n\n      initialized = true;\n\n      if (worker) {\n        canvas.__confetti_initialized = true;\n      }\n\n      function onResize() {\n        if (worker) {\n          // TODO this really shouldn't be immediate, because it is expensive\n          var obj = {\n            getBoundingClientRect: function () {\n              if (!isLibCanvas) {\n                return canvas.getBoundingClientRect();\n              }\n            }\n          };\n\n          resizer(obj);\n\n          worker.postMessage({\n            resize: {\n              width: obj.width,\n              height: obj.height\n            }\n          });\n          return;\n        }\n\n        // don't actually query the size here, since this\n        // can execute frequently and rapidly\n        size.width = size.height = null;\n      }\n\n      function done() {\n        animationObj = null;\n\n        if (allowResize) {\n          hasResizeEventRegistered = false;\n          global.removeEventListener('resize', onResize);\n        }\n\n        if (isLibCanvas && canvas) {\n          if (document.body.contains(canvas)) {\n            document.body.removeChild(canvas); \n          }\n          canvas = null;\n          initialized = false;\n        }\n      }\n\n      if (allowResize && !hasResizeEventRegistered) {\n        hasResizeEventRegistered = true;\n        global.addEventListener('resize', onResize, false);\n      }\n\n      if (worker) {\n        return worker.fire(options, size, done);\n      }\n\n      return fireLocal(options, size, done);\n    }\n\n    fire.reset = function () {\n      if (worker) {\n        worker.reset();\n      }\n\n      if (animationObj) {\n        animationObj.reset();\n      }\n    };\n\n    return fire;\n  }\n\n  // Make default export lazy to defer worker creation until called.\n  var defaultFire;\n  function getDefaultFire() {\n    if (!defaultFire) {\n      defaultFire = confettiCannon(null, { useWorker: true, resize: true });\n    }\n    return defaultFire;\n  }\n\n  function transformPath2D(pathString, pathMatrix, x, y, scaleX, scaleY, rotation) {\n    var path2d = new Path2D(pathString);\n\n    var t1 = new Path2D();\n    t1.addPath(path2d, new DOMMatrix(pathMatrix));\n\n    var t2 = new Path2D();\n    // see https://developer.mozilla.org/en-US/docs/Web/API/DOMMatrix/DOMMatrix\n    t2.addPath(t1, new DOMMatrix([\n      Math.cos(rotation) * scaleX,\n      Math.sin(rotation) * scaleX,\n      -Math.sin(rotation) * scaleY,\n      Math.cos(rotation) * scaleY,\n      x,\n      y\n    ]));\n\n    return t2;\n  }\n\n  function shapeFromPath(pathData) {\n    if (!canUsePaths) {\n      throw new Error('path confetti are not supported in this browser');\n    }\n\n    var path, matrix;\n\n    if (typeof pathData === 'string') {\n      path = pathData;\n    } else {\n      path = pathData.path;\n      matrix = pathData.matrix;\n    }\n\n    var path2d = new Path2D(path);\n    var tempCanvas = document.createElement('canvas');\n    var tempCtx = tempCanvas.getContext('2d');\n\n    if (!matrix) {\n      // attempt to figure out the width of the path, up to 1000x1000\n      var maxSize = 1000;\n      var minX = maxSize;\n      var minY = maxSize;\n      var maxX = 0;\n      var maxY = 0;\n      var width, height;\n\n      // do some line skipping... this is faster than checking\n      // every pixel and will be mostly still correct\n      for (var x = 0; x < maxSize; x += 2) {\n        for (var y = 0; y < maxSize; y += 2) {\n          if (tempCtx.isPointInPath(path2d, x, y, 'nonzero')) {\n            minX = Math.min(minX, x);\n            minY = Math.min(minY, y);\n            maxX = Math.max(maxX, x);\n            maxY = Math.max(maxY, y);\n          }\n        }\n      }\n\n      width = maxX - minX;\n      height = maxY - minY;\n\n      var maxDesiredSize = 10;\n      var scale = Math.min(maxDesiredSize/width, maxDesiredSize/height);\n\n      matrix = [\n        scale, 0, 0, scale,\n        -Math.round((width/2) + minX) * scale,\n        -Math.round((height/2) + minY) * scale\n      ];\n    }\n\n    return {\n      type: 'path',\n      path: path,\n      matrix: matrix\n    };\n  }\n\n  function shapeFromText(textData) {\n    var text,\n        scalar = 1,\n        color = '#000000',\n        // see https://nolanlawson.com/2022/04/08/the-struggle-of-using-native-emoji-on-the-web/\n        fontFamily = '\"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\", \"EmojiOne Color\", \"Android Emoji\", \"Twemoji Mozilla\", \"system emoji\", sans-serif';\n\n    if (typeof textData === 'string') {\n      text = textData;\n    } else {\n      text = textData.text;\n      scalar = 'scalar' in textData ? textData.scalar : scalar;\n      fontFamily = 'fontFamily' in textData ? textData.fontFamily : fontFamily;\n      color = 'color' in textData ? textData.color : color;\n    }\n\n    // all other confetti are 10 pixels,\n    // so this pixel size is the de-facto 100% scale confetti\n    var fontSize = 10 * scalar;\n    var font = '' + fontSize + 'px ' + fontFamily;\n\n    var canvas = new OffscreenCanvas(fontSize, fontSize);\n    var ctx = canvas.getContext('2d');\n\n    ctx.font = font;\n    var size = ctx.measureText(text);\n    var width = Math.ceil(size.actualBoundingBoxRight + size.actualBoundingBoxLeft);\n    var height = Math.ceil(size.actualBoundingBoxAscent + size.actualBoundingBoxDescent);\n\n    var padding = 2;\n    var x = size.actualBoundingBoxLeft + padding;\n    var y = size.actualBoundingBoxAscent + padding;\n    width += padding + padding;\n    height += padding + padding;\n\n    canvas = new OffscreenCanvas(width, height);\n    ctx = canvas.getContext('2d');\n    ctx.font = font;\n    ctx.fillStyle = color;\n\n    ctx.fillText(text, x, y);\n\n    var scale = 1 / scalar;\n\n    return {\n      type: 'bitmap',\n      // TODO these probably need to be transfered for workers\n      bitmap: canvas.transferToImageBitmap(),\n      matrix: [scale, 0, 0, scale, -width * scale / 2, -height * scale / 2]\n    };\n  }\n\n  module.exports = function() {\n    return getDefaultFire().apply(this, arguments);\n  };\n  module.exports.reset = function() {\n    getDefaultFire().reset();\n  };\n  module.exports.create = confettiCannon;\n  module.exports.shapeFromPath = shapeFromPath;\n  module.exports.shapeFromText = shapeFromText;\n}((function () {\n  if (typeof window !== 'undefined') {\n    return window;\n  }\n\n  if (typeof self !== 'undefined') {\n    return self;\n  }\n\n  return this || {};\n})(), module, false));\n\n// end source content\n\nexport default module.exports;\nexport var create = module.exports.create;\n"], "mappings": ";;;AACA,IAAI,SAAS,CAAC;AAAA,CAKb,SAAS,KAAK,QAAQA,SAAQ,UAAU,YAAY;AACnD,MAAI,eAAe,CAAC,EAClB,OAAO,UACP,OAAO,QACP,OAAO,WACP,OAAO,mBACP,OAAO,qCACP,OAAO,qBACP,OAAO,kBAAkB,UAAU,8BACnC,OAAO,OACP,OAAO,IAAI;AAEb,MAAI,cAAc,OAAO,WAAW,cAAc,OAAO,cAAc;AACvE,MAAI,gBAAiB,WAAY;AAE/B,QAAI,CAAC,OAAO,iBAAiB;AAC3B,aAAO;AAAA,IACT;AAEA,QAAI,SAAS,IAAI,gBAAgB,GAAG,CAAC;AACrC,QAAI,MAAM,OAAO,WAAW,IAAI;AAChC,QAAI,SAAS,GAAG,GAAG,GAAG,CAAC;AACvB,QAAI,SAAS,OAAO,sBAAsB;AAE1C,QAAI;AACF,UAAI,cAAc,QAAQ,WAAW;AAAA,IACvC,SAAS,GAAG;AACV,aAAO;AAAA,IACT;AAEA,WAAO;AAAA,EACT,EAAG;AAEH,WAAS,OAAO;AAAA,EAAC;AAIjB,WAAS,QAAQ,MAAM;AACrB,QAAI,gBAAgBA,QAAO,QAAQ;AACnC,QAAI,OAAO,kBAAkB,SAAS,gBAAgB,OAAO;AAE7D,QAAI,OAAO,SAAS,YAAY;AAC9B,aAAO,IAAI,KAAK,IAAI;AAAA,IACtB;AAEA,SAAK,MAAM,IAAI;AAEf,WAAO;AAAA,EACT;AAEA,MAAI,eAAgB,yBAAU,eAAe,KAAK;AAMhD,WAAO;AAAA,MACL,WAAW,SAAS,QAAQ;AAC1B,YAAI,eAAe;AACjB,iBAAO;AAAA,QACT;AAEA,YAAI,IAAI,IAAI,MAAM,GAAG;AACnB,iBAAO,IAAI,IAAI,MAAM;AAAA,QACvB;AAEA,YAAI,SAAS,IAAI,gBAAgB,OAAO,OAAO,OAAO,MAAM;AAC5D,YAAI,MAAM,OAAO,WAAW,IAAI;AAChC,YAAI,UAAU,QAAQ,GAAG,CAAC;AAE1B,YAAI,IAAI,QAAQ,MAAM;AAEtB,eAAO;AAAA,MACT;AAAA,MACA,OAAO,WAAY;AACjB,YAAI,MAAM;AAAA,MACZ;AAAA,IACF;AAAA,EACF,EAAG,eAAe,oBAAI,IAAI,CAAC;AAE3B,MAAI,MAAO,WAAY;AACrB,QAAI,OAAO,KAAK,MAAM,MAAO,EAAE;AAC/B,QAAI,OAAO;AACX,QAAI,SAAS,CAAC;AACd,QAAI,gBAAgB;AAEpB,QAAI,OAAO,0BAA0B,cAAc,OAAO,yBAAyB,YAAY;AAC7F,cAAQ,SAAU,IAAI;AACpB,YAAI,KAAK,KAAK,OAAO;AAErB,eAAO,EAAE,IAAI,sBAAsB,SAAS,QAAQ,MAAM;AACxD,cAAI,kBAAkB,QAAQ,gBAAgB,OAAO,IAAI,MAAM;AAC7D,4BAAgB;AAChB,mBAAO,OAAO,EAAE;AAEhB,eAAG;AAAA,UACL,OAAO;AACL,mBAAO,EAAE,IAAI,sBAAsB,OAAO;AAAA,UAC5C;AAAA,QACF,CAAC;AAED,eAAO;AAAA,MACT;AACA,eAAS,SAAU,IAAI;AACrB,YAAI,OAAO,EAAE,GAAG;AACd,+BAAqB,OAAO,EAAE,CAAC;AAAA,QACjC;AAAA,MACF;AAAA,IACF,OAAO;AACL,cAAQ,SAAU,IAAI;AACpB,eAAO,WAAW,IAAI,IAAI;AAAA,MAC5B;AACA,eAAS,SAAU,OAAO;AACxB,eAAO,aAAa,KAAK;AAAA,MAC3B;AAAA,IACF;AAEA,WAAO,EAAE,OAAc,OAAe;AAAA,EACxC,EAAE;AAEF,MAAI,YAAa,2BAAY;AAC3B,QAAI;AACJ,QAAI;AACJ,QAAI,WAAW,CAAC;AAEhB,aAAS,SAASC,SAAQ;AACxB,eAAS,QAAQ,SAAS,UAAU;AAClC,QAAAA,QAAO,YAAY,EAAE,SAAS,WAAW,CAAC,GAAG,SAAmB,CAAC;AAAA,MACnE;AACA,MAAAA,QAAO,OAAO,SAAS,WAAW,QAAQ;AACxC,YAAI,YAAY,OAAO,2BAA2B;AAClD,QAAAA,QAAO,YAAY,EAAE,QAAQ,UAAU,GAAG,CAAC,SAAS,CAAC;AAAA,MACvD;AAEA,MAAAA,QAAO,OAAO,SAAS,WAAW,SAAS,MAAM,MAAM;AACrD,YAAI,MAAM;AACR,kBAAQ,SAAS,IAAI;AACrB,iBAAO;AAAA,QACT;AAEA,YAAI,KAAK,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,MAAM,CAAC;AAE3C,eAAO,QAAQ,SAAU,SAAS;AAChC,mBAAS,WAAW,KAAK;AACvB,gBAAI,IAAI,KAAK,aAAa,IAAI;AAC5B;AAAA,YACF;AAEA,mBAAO,SAAS,EAAE;AAClB,YAAAA,QAAO,oBAAoB,WAAW,UAAU;AAEhD,mBAAO;AAEP,yBAAa,MAAM;AAEnB,iBAAK;AACL,oBAAQ;AAAA,UACV;AAEA,UAAAA,QAAO,iBAAiB,WAAW,UAAU;AAC7C,kBAAQ,SAAS,EAAE;AAEnB,mBAAS,EAAE,IAAI,WAAW,KAAK,MAAM,EAAE,MAAM,EAAE,UAAU,GAAG,EAAC,CAAC;AAAA,QAChE,CAAC;AAED,eAAO;AAAA,MACT;AAEA,MAAAA,QAAO,QAAQ,SAAS,cAAc;AACpC,QAAAA,QAAO,YAAY,EAAE,OAAO,KAAK,CAAC;AAElC,iBAAS,MAAM,UAAU;AACvB,mBAAS,EAAE,EAAE;AACb,iBAAO,SAAS,EAAE;AAAA,QACpB;AAAA,MACF;AAAA,IACF;AAEA,WAAO,WAAY;AACjB,UAAI,QAAQ;AACV,eAAO;AAAA,MACT;AAEA,UAAI,CAAC,YAAY,cAAc;AAC7B,YAAI,OAAO;AAAA,UACT;AAAA,UACA,MAAM,KAAK,SAAS,IAAI;AAAA,UACxB;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,EAAE,KAAK,IAAI;AACX,YAAI;AACF,mBAAS,IAAI,OAAO,IAAI,gBAAgB,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;AAAA,QAC3D,SAAS,GAAG;AAEV,iBAAO,YAAY,UAAa,OAAO,QAAQ,SAAS,aAAa,QAAQ,KAAK,4BAA4B,CAAC,IAAI;AAEnH,iBAAO;AAAA,QACT;AAEA,iBAAS,MAAM;AAAA,MACjB;AAEA,aAAO;AAAA,IACT;AAAA,EACF,EAAG;AAEH,MAAI,WAAW;AAAA,IACb,eAAe;AAAA,IACf,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,OAAO;AAAA,IACP,SAAS;AAAA,IACT,OAAO;AAAA,IACP,OAAO;AAAA,IACP,GAAG;AAAA,IACH,GAAG;AAAA,IACH,QAAQ,CAAC,UAAU,QAAQ;AAAA,IAC3B,QAAQ;AAAA,IACR,QAAQ;AAAA,MACN;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA;AAAA,IAEA,yBAAyB;AAAA,IACzB,QAAQ;AAAA,EACV;AAEA,WAAS,QAAQ,KAAK,WAAW;AAC/B,WAAO,YAAY,UAAU,GAAG,IAAI;AAAA,EACtC;AAEA,WAAS,KAAK,KAAK;AACjB,WAAO,EAAE,QAAQ,QAAQ,QAAQ;AAAA,EACnC;AAEA,WAAS,KAAK,SAAS,MAAM,WAAW;AACtC,WAAO;AAAA,MACL,WAAW,KAAK,QAAQ,IAAI,CAAC,IAAI,QAAQ,IAAI,IAAI,SAAS,IAAI;AAAA,MAC9D;AAAA,IACF;AAAA,EACF;AAEA,WAAS,gBAAgB,QAAO;AAC9B,WAAO,SAAS,IAAI,IAAI,KAAK,MAAM,MAAM;AAAA,EAC3C;AAEA,WAAS,UAAU,KAAK,KAAK;AAE3B,WAAO,KAAK,MAAM,KAAK,OAAO,KAAK,MAAM,IAAI,IAAI;AAAA,EACnD;AAEA,WAAS,UAAU,KAAK;AACtB,WAAO,SAAS,KAAK,EAAE;AAAA,EACzB;AAEA,WAAS,YAAY,QAAQ;AAC3B,WAAO,OAAO,IAAI,QAAQ;AAAA,EAC5B;AAEA,WAAS,SAAS,KAAK;AACrB,QAAI,MAAM,OAAO,GAAG,EAAE,QAAQ,eAAe,EAAE;AAE/C,QAAI,IAAI,SAAS,GAAG;AAChB,YAAM,IAAI,CAAC,IAAE,IAAI,CAAC,IAAE,IAAI,CAAC,IAAE,IAAI,CAAC,IAAE,IAAI,CAAC,IAAE,IAAI,CAAC;AAAA,IAClD;AAEA,WAAO;AAAA,MACL,GAAG,UAAU,IAAI,UAAU,GAAE,CAAC,CAAC;AAAA,MAC/B,GAAG,UAAU,IAAI,UAAU,GAAE,CAAC,CAAC;AAAA,MAC/B,GAAG,UAAU,IAAI,UAAU,GAAE,CAAC,CAAC;AAAA,IACjC;AAAA,EACF;AAEA,WAAS,UAAU,SAAS;AAC1B,QAAI,SAAS,KAAK,SAAS,UAAU,MAAM;AAC3C,WAAO,IAAI,KAAK,QAAQ,KAAK,MAAM;AACnC,WAAO,IAAI,KAAK,QAAQ,KAAK,MAAM;AAEnC,WAAO;AAAA,EACT;AAEA,WAAS,oBAAoB,QAAQ;AACnC,WAAO,QAAQ,SAAS,gBAAgB;AACxC,WAAO,SAAS,SAAS,gBAAgB;AAAA,EAC3C;AAEA,WAAS,kBAAkB,QAAQ;AACjC,QAAI,OAAO,OAAO,sBAAsB;AACxC,WAAO,QAAQ,KAAK;AACpB,WAAO,SAAS,KAAK;AAAA,EACvB;AAEA,WAAS,UAAU,QAAQ;AACzB,QAAI,SAAS,SAAS,cAAc,QAAQ;AAE5C,WAAO,MAAM,WAAW;AACxB,WAAO,MAAM,MAAM;AACnB,WAAO,MAAM,OAAO;AACpB,WAAO,MAAM,gBAAgB;AAC7B,WAAO,MAAM,SAAS;AAEtB,WAAO;AAAA,EACT;AAEA,WAAS,QAAQ,SAAS,GAAG,GAAG,SAAS,SAAS,UAAU,YAAY,UAAU,eAAe;AAC/F,YAAQ,KAAK;AACb,YAAQ,UAAU,GAAG,CAAC;AACtB,YAAQ,OAAO,QAAQ;AACvB,YAAQ,MAAM,SAAS,OAAO;AAC9B,YAAQ,IAAI,GAAG,GAAG,GAAG,YAAY,UAAU,aAAa;AACxD,YAAQ,QAAQ;AAAA,EAClB;AAEA,WAAS,cAAc,MAAM;AAC3B,QAAI,WAAW,KAAK,SAAS,KAAK,KAAK;AACvC,QAAI,YAAY,KAAK,UAAU,KAAK,KAAK;AAEzC,WAAO;AAAA,MACL,GAAG,KAAK;AAAA,MACR,GAAG,KAAK;AAAA,MACR,QAAQ,KAAK,OAAO,IAAI;AAAA,MACxB,aAAa,KAAK,IAAI,MAAM,KAAK,OAAO,IAAI,MAAM,IAAI;AAAA,MACtD,UAAW,KAAK,gBAAgB,MAAQ,KAAK,OAAO,IAAI,KAAK;AAAA,MAC7D,SAAS,CAAC,YAAa,MAAM,YAAc,KAAK,OAAO,IAAI;AAAA,MAC3D,YAAY,KAAK,OAAO,KAAK,OAAO,QAAQ,QAAQ,KAAK;AAAA,MACzD,OAAO,KAAK;AAAA,MACZ,OAAO,KAAK;AAAA,MACZ,MAAM;AAAA,MACN,YAAY,KAAK;AAAA,MACjB,OAAO,KAAK;AAAA,MACZ,OAAO,KAAK;AAAA,MACZ,QAAQ,KAAK,OAAO,IAAI;AAAA,MACxB,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS;AAAA,MACT,SAAS,KAAK,UAAU;AAAA,MACxB,YAAY;AAAA,MACZ,QAAQ,KAAK;AAAA,MACb,MAAM,KAAK;AAAA,IACb;AAAA,EACF;AAEA,WAAS,YAAY,SAAS,OAAO;AACnC,UAAM,KAAK,KAAK,IAAI,MAAM,OAAO,IAAI,MAAM,WAAW,MAAM;AAC5D,UAAM,KAAK,KAAK,IAAI,MAAM,OAAO,IAAI,MAAM,WAAW,MAAM;AAC5D,UAAM,YAAY,MAAM;AAExB,QAAI,MAAM,MAAM;AACd,YAAM,SAAS;AACf,YAAM,UAAU,MAAM,IAAK,KAAK,MAAM;AACtC,YAAM,UAAU,MAAM,IAAK,KAAK,MAAM;AAEtC,YAAM,UAAU;AAChB,YAAM,UAAU;AAChB,YAAM,SAAS;AAAA,IACjB,OAAO;AACL,YAAM,UAAU,MAAM;AACtB,YAAM,UAAU,MAAM,IAAM,KAAK,MAAM,SAAU,KAAK,IAAI,MAAM,MAAM;AACtE,YAAM,UAAU,MAAM,IAAM,KAAK,MAAM,SAAU,KAAK,IAAI,MAAM,MAAM;AAEtE,YAAM,aAAa;AACnB,YAAM,UAAU,KAAK,IAAI,MAAM,SAAS;AACxC,YAAM,UAAU,KAAK,IAAI,MAAM,SAAS;AACxC,YAAM,SAAS,KAAK,OAAO,IAAI;AAAA,IACjC;AAEA,QAAI,WAAY,MAAM,SAAU,MAAM;AAEtC,QAAI,KAAK,MAAM,IAAK,MAAM,SAAS,MAAM;AACzC,QAAI,KAAK,MAAM,IAAK,MAAM,SAAS,MAAM;AACzC,QAAI,KAAK,MAAM,UAAW,MAAM,SAAS,MAAM;AAC/C,QAAI,KAAK,MAAM,UAAW,MAAM,SAAS,MAAM;AAE/C,YAAQ,YAAY,UAAU,MAAM,MAAM,IAAI,OAAO,MAAM,MAAM,IAAI,OAAO,MAAM,MAAM,IAAI,QAAQ,IAAI,YAAY;AAEpH,YAAQ,UAAU;AAElB,QAAI,eAAe,MAAM,MAAM,SAAS,UAAU,OAAO,MAAM,MAAM,SAAS,YAAY,MAAM,QAAQ,MAAM,MAAM,MAAM,GAAG;AAC3H,cAAQ,KAAK;AAAA,QACX,MAAM,MAAM;AAAA,QACZ,MAAM,MAAM;AAAA,QACZ,MAAM;AAAA,QACN,MAAM;AAAA,QACN,KAAK,IAAI,KAAK,EAAE,IAAI;AAAA,QACpB,KAAK,IAAI,KAAK,EAAE,IAAI;AAAA,QACpB,KAAK,KAAK,KAAK,MAAM;AAAA,MACvB,CAAC;AAAA,IACH,WAAW,MAAM,MAAM,SAAS,UAAU;AACxC,UAAI,WAAW,KAAK,KAAK,KAAK,MAAM;AACpC,UAAI,SAAS,KAAK,IAAI,KAAK,EAAE,IAAI;AACjC,UAAI,SAAS,KAAK,IAAI,KAAK,EAAE,IAAI;AACjC,UAAI,QAAQ,MAAM,MAAM,OAAO,QAAQ,MAAM;AAC7C,UAAI,SAAS,MAAM,MAAM,OAAO,SAAS,MAAM;AAE/C,UAAI,SAAS,IAAI,UAAU;AAAA,QACzB,KAAK,IAAI,QAAQ,IAAI;AAAA,QACrB,KAAK,IAAI,QAAQ,IAAI;AAAA,QACrB,CAAC,KAAK,IAAI,QAAQ,IAAI;AAAA,QACtB,KAAK,IAAI,QAAQ,IAAI;AAAA,QACrB,MAAM;AAAA,QACN,MAAM;AAAA,MACR,CAAC;AAGD,aAAO,aAAa,IAAI,UAAU,MAAM,MAAM,MAAM,CAAC;AAErD,UAAI,UAAU,QAAQ,cAAc,aAAa,UAAU,MAAM,MAAM,MAAM,GAAG,WAAW;AAC3F,cAAQ,aAAa,MAAM;AAE3B,cAAQ,cAAe,IAAI;AAC3B,cAAQ,YAAY;AACpB,cAAQ;AAAA,QACN,MAAM,IAAK,QAAQ;AAAA,QACnB,MAAM,IAAK,SAAS;AAAA,QACpB;AAAA,QACA;AAAA,MACF;AACA,cAAQ,cAAc;AAAA,IACxB,WAAW,MAAM,UAAU,UAAU;AACnC,cAAQ,UACN,QAAQ,QAAQ,MAAM,GAAG,MAAM,GAAG,KAAK,IAAI,KAAK,EAAE,IAAI,MAAM,YAAY,KAAK,IAAI,KAAK,EAAE,IAAI,MAAM,YAAY,KAAK,KAAK,KAAK,MAAM,QAAQ,GAAG,IAAI,KAAK,EAAE,IACzJ,QAAQ,SAAS,MAAM,GAAG,MAAM,GAAG,KAAK,IAAI,KAAK,EAAE,IAAI,MAAM,YAAY,KAAK,IAAI,KAAK,EAAE,IAAI,MAAM,YAAY,KAAK,KAAK,KAAK,MAAM,QAAQ,GAAG,IAAI,KAAK,EAAE;AAAA,IAC9J,WAAW,MAAM,UAAU,QAAQ;AACjC,UAAI,MAAM,KAAK,KAAK,IAAI;AACxB,UAAI,cAAc,IAAI,MAAM;AAC5B,UAAI,cAAc,IAAI,MAAM;AAC5B,UAAI,IAAI,MAAM;AACd,UAAI,IAAI,MAAM;AACd,UAAI,SAAS;AACb,UAAI,OAAO,KAAK,KAAK;AAErB,aAAO,UAAU;AACf,YAAI,MAAM,IAAI,KAAK,IAAI,GAAG,IAAI;AAC9B,YAAI,MAAM,IAAI,KAAK,IAAI,GAAG,IAAI;AAC9B,gBAAQ,OAAO,GAAG,CAAC;AACnB,eAAO;AAEP,YAAI,MAAM,IAAI,KAAK,IAAI,GAAG,IAAI;AAC9B,YAAI,MAAM,IAAI,KAAK,IAAI,GAAG,IAAI;AAC9B,gBAAQ,OAAO,GAAG,CAAC;AACnB,eAAO;AAAA,MACT;AAAA,IACF,OAAO;AACL,cAAQ,OAAO,KAAK,MAAM,MAAM,CAAC,GAAG,KAAK,MAAM,MAAM,CAAC,CAAC;AACvD,cAAQ,OAAO,KAAK,MAAM,MAAM,OAAO,GAAG,KAAK,MAAM,EAAE,CAAC;AACxD,cAAQ,OAAO,KAAK,MAAM,EAAE,GAAG,KAAK,MAAM,EAAE,CAAC;AAC7C,cAAQ,OAAO,KAAK,MAAM,EAAE,GAAG,KAAK,MAAM,MAAM,OAAO,CAAC;AAAA,IAC1D;AAEA,YAAQ,UAAU;AAClB,YAAQ,KAAK;AAEb,WAAO,MAAM,OAAO,MAAM;AAAA,EAC5B;AAEA,WAAS,QAAQ,QAAQ,QAAQ,SAAS,MAAM,MAAM;AACpD,QAAI,kBAAkB,OAAO,MAAM;AACnC,QAAI,UAAU,OAAO,WAAW,IAAI;AACpC,QAAI;AACJ,QAAI;AAEJ,QAAI,OAAO,QAAQ,SAAU,SAAS;AACpC,eAAS,SAAS;AAChB,yBAAiB,UAAU;AAE3B,gBAAQ,UAAU,GAAG,GAAG,KAAK,OAAO,KAAK,MAAM;AAC/C,qBAAa,MAAM;AAEnB,aAAK;AACL,gBAAQ;AAAA,MACV;AAEA,eAAS,SAAS;AAChB,YAAI,YAAY,EAAE,KAAK,UAAU,WAAW,SAAS,KAAK,WAAW,WAAW,SAAS;AACvF,eAAK,QAAQ,OAAO,QAAQ,WAAW;AACvC,eAAK,SAAS,OAAO,SAAS,WAAW;AAAA,QAC3C;AAEA,YAAI,CAAC,KAAK,SAAS,CAAC,KAAK,QAAQ;AAC/B,kBAAQ,MAAM;AACd,eAAK,QAAQ,OAAO;AACpB,eAAK,SAAS,OAAO;AAAA,QACvB;AAEA,gBAAQ,UAAU,GAAG,GAAG,KAAK,OAAO,KAAK,MAAM;AAE/C,0BAAkB,gBAAgB,OAAO,SAAU,OAAO;AACxD,iBAAO,YAAY,SAAS,KAAK;AAAA,QACnC,CAAC;AAED,YAAI,gBAAgB,QAAQ;AAC1B,2BAAiB,IAAI,MAAM,MAAM;AAAA,QACnC,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,uBAAiB,IAAI,MAAM,MAAM;AACjC,gBAAU;AAAA,IACZ,CAAC;AAED,WAAO;AAAA,MACL,WAAW,SAAUC,SAAQ;AAC3B,0BAAkB,gBAAgB,OAAOA,OAAM;AAE/C,eAAO;AAAA,MACT;AAAA,MACA;AAAA,MACA,SAAS;AAAA,MACT,OAAO,WAAY;AACjB,YAAI,gBAAgB;AAClB,cAAI,OAAO,cAAc;AAAA,QAC3B;AAEA,YAAI,SAAS;AACX,kBAAQ;AAAA,QACV;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,WAAS,eAAe,QAAQ,YAAY;AAC1C,QAAI,cAAc,CAAC;AACnB,QAAI,cAAc,CAAC,CAAC,KAAK,cAAc,CAAC,GAAG,QAAQ;AACnD,QAAI,2BAA2B;AAC/B,QAAI,gCAAgC,KAAK,YAAY,2BAA2B,OAAO;AACvF,QAAI,kBAAkB,gBAAgB,CAAC,CAAC,KAAK,cAAc,CAAC,GAAG,WAAW;AAC1E,QAAI,SAAS,kBAAkB,UAAU,IAAI;AAC7C,QAAI,UAAU,cAAc,sBAAsB;AAClD,QAAI,cAAe,UAAU,SAAU,CAAC,CAAC,OAAO,yBAAyB;AACzE,QAAI,mBAAmB,OAAO,eAAe,cAAc,WAAW,0BAA0B,EAAE;AAClG,QAAI;AAEJ,aAAS,UAAU,SAAS,MAAM,MAAM;AACtC,UAAI,gBAAgB,KAAK,SAAS,iBAAiB,eAAe;AAClE,UAAI,QAAQ,KAAK,SAAS,SAAS,MAAM;AACzC,UAAI,SAAS,KAAK,SAAS,UAAU,MAAM;AAC3C,UAAI,gBAAgB,KAAK,SAAS,iBAAiB,MAAM;AACzD,UAAI,QAAQ,KAAK,SAAS,SAAS,MAAM;AACzC,UAAI,UAAU,KAAK,SAAS,WAAW,MAAM;AAC7C,UAAI,QAAQ,KAAK,SAAS,SAAS,MAAM;AACzC,UAAI,SAAS,KAAK,SAAS,UAAU,WAAW;AAChD,UAAI,QAAQ,KAAK,SAAS,SAAS,MAAM;AACzC,UAAI,SAAS,KAAK,SAAS,QAAQ;AACnC,UAAI,SAAS,KAAK,SAAS,QAAQ;AACnC,UAAI,OAAO,CAAC,CAAC,KAAK,SAAS,MAAM;AACjC,UAAI,SAAS,UAAU,OAAO;AAE9B,UAAI,OAAO;AACX,UAAI,SAAS,CAAC;AAEd,UAAI,SAAS,OAAO,QAAQ,OAAO;AACnC,UAAI,SAAS,OAAO,SAAS,OAAO;AAEpC,aAAO,QAAQ;AACb,eAAO;AAAA,UACL,cAAc;AAAA,YACZ,GAAG;AAAA,YACH,GAAG;AAAA,YACH;AAAA,YACA;AAAA,YACA;AAAA,YACA,OAAO,OAAO,OAAO,OAAO,MAAM;AAAA,YAClC,OAAO,OAAO,UAAU,GAAG,OAAO,MAAM,CAAC;AAAA,YACzC;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF;AAIA,UAAI,cAAc;AAChB,eAAO,aAAa,UAAU,MAAM;AAAA,MACtC;AAEA,qBAAe,QAAQ,QAAQ,QAAQ,SAAS,MAAO,IAAI;AAE3D,aAAO,aAAa;AAAA,IACtB;AAEA,aAAS,KAAK,SAAS;AACrB,UAAI,0BAA0B,iCAAiC,KAAK,SAAS,2BAA2B,OAAO;AAC/G,UAAI,SAAS,KAAK,SAAS,UAAU,MAAM;AAE3C,UAAI,2BAA2B,kBAAkB;AAC/C,eAAO,QAAQ,SAAU,SAAS;AAChC,kBAAQ;AAAA,QACV,CAAC;AAAA,MACH;AAEA,UAAI,eAAe,cAAc;AAE/B,iBAAS,aAAa;AAAA,MACxB,WAAW,eAAe,CAAC,QAAQ;AAEjC,iBAAS,UAAU,MAAM;AACzB,iBAAS,KAAK,YAAY,MAAM;AAAA,MAClC;AAEA,UAAI,eAAe,CAAC,aAAa;AAE/B,gBAAQ,MAAM;AAAA,MAChB;AAEA,UAAI,OAAO;AAAA,QACT,OAAO,OAAO;AAAA,QACd,QAAQ,OAAO;AAAA,MACjB;AAEA,UAAI,UAAU,CAAC,aAAa;AAC1B,eAAO,KAAK,MAAM;AAAA,MACpB;AAEA,oBAAc;AAEd,UAAI,QAAQ;AACV,eAAO,yBAAyB;AAAA,MAClC;AAEA,eAAS,WAAW;AAClB,YAAI,QAAQ;AAEV,cAAI,MAAM;AAAA,YACR,uBAAuB,WAAY;AACjC,kBAAI,CAAC,aAAa;AAChB,uBAAO,OAAO,sBAAsB;AAAA,cACtC;AAAA,YACF;AAAA,UACF;AAEA,kBAAQ,GAAG;AAEX,iBAAO,YAAY;AAAA,YACjB,QAAQ;AAAA,cACN,OAAO,IAAI;AAAA,cACX,QAAQ,IAAI;AAAA,YACd;AAAA,UACF,CAAC;AACD;AAAA,QACF;AAIA,aAAK,QAAQ,KAAK,SAAS;AAAA,MAC7B;AAEA,eAAS,OAAO;AACd,uBAAe;AAEf,YAAI,aAAa;AACf,qCAA2B;AAC3B,iBAAO,oBAAoB,UAAU,QAAQ;AAAA,QAC/C;AAEA,YAAI,eAAe,QAAQ;AACzB,cAAI,SAAS,KAAK,SAAS,MAAM,GAAG;AAClC,qBAAS,KAAK,YAAY,MAAM;AAAA,UAClC;AACA,mBAAS;AACT,wBAAc;AAAA,QAChB;AAAA,MACF;AAEA,UAAI,eAAe,CAAC,0BAA0B;AAC5C,mCAA2B;AAC3B,eAAO,iBAAiB,UAAU,UAAU,KAAK;AAAA,MACnD;AAEA,UAAI,QAAQ;AACV,eAAO,OAAO,KAAK,SAAS,MAAM,IAAI;AAAA,MACxC;AAEA,aAAO,UAAU,SAAS,MAAM,IAAI;AAAA,IACtC;AAEA,SAAK,QAAQ,WAAY;AACvB,UAAI,QAAQ;AACV,eAAO,MAAM;AAAA,MACf;AAEA,UAAI,cAAc;AAChB,qBAAa,MAAM;AAAA,MACrB;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAGA,MAAI;AACJ,WAAS,iBAAiB;AACxB,QAAI,CAAC,aAAa;AAChB,oBAAc,eAAe,MAAM,EAAE,WAAW,MAAM,QAAQ,KAAK,CAAC;AAAA,IACtE;AACA,WAAO;AAAA,EACT;AAEA,WAAS,gBAAgB,YAAY,YAAY,GAAG,GAAG,QAAQ,QAAQ,UAAU;AAC/E,QAAI,SAAS,IAAI,OAAO,UAAU;AAElC,QAAI,KAAK,IAAI,OAAO;AACpB,OAAG,QAAQ,QAAQ,IAAI,UAAU,UAAU,CAAC;AAE5C,QAAI,KAAK,IAAI,OAAO;AAEpB,OAAG,QAAQ,IAAI,IAAI,UAAU;AAAA,MAC3B,KAAK,IAAI,QAAQ,IAAI;AAAA,MACrB,KAAK,IAAI,QAAQ,IAAI;AAAA,MACrB,CAAC,KAAK,IAAI,QAAQ,IAAI;AAAA,MACtB,KAAK,IAAI,QAAQ,IAAI;AAAA,MACrB;AAAA,MACA;AAAA,IACF,CAAC,CAAC;AAEF,WAAO;AAAA,EACT;AAEA,WAAS,cAAc,UAAU;AAC/B,QAAI,CAAC,aAAa;AAChB,YAAM,IAAI,MAAM,iDAAiD;AAAA,IACnE;AAEA,QAAI,MAAM;AAEV,QAAI,OAAO,aAAa,UAAU;AAChC,aAAO;AAAA,IACT,OAAO;AACL,aAAO,SAAS;AAChB,eAAS,SAAS;AAAA,IACpB;AAEA,QAAI,SAAS,IAAI,OAAO,IAAI;AAC5B,QAAI,aAAa,SAAS,cAAc,QAAQ;AAChD,QAAI,UAAU,WAAW,WAAW,IAAI;AAExC,QAAI,CAAC,QAAQ;AAEX,UAAI,UAAU;AACd,UAAI,OAAO;AACX,UAAI,OAAO;AACX,UAAI,OAAO;AACX,UAAI,OAAO;AACX,UAAI,OAAO;AAIX,eAAS,IAAI,GAAG,IAAI,SAAS,KAAK,GAAG;AACnC,iBAAS,IAAI,GAAG,IAAI,SAAS,KAAK,GAAG;AACnC,cAAI,QAAQ,cAAc,QAAQ,GAAG,GAAG,SAAS,GAAG;AAClD,mBAAO,KAAK,IAAI,MAAM,CAAC;AACvB,mBAAO,KAAK,IAAI,MAAM,CAAC;AACvB,mBAAO,KAAK,IAAI,MAAM,CAAC;AACvB,mBAAO,KAAK,IAAI,MAAM,CAAC;AAAA,UACzB;AAAA,QACF;AAAA,MACF;AAEA,cAAQ,OAAO;AACf,eAAS,OAAO;AAEhB,UAAI,iBAAiB;AACrB,UAAI,QAAQ,KAAK,IAAI,iBAAe,OAAO,iBAAe,MAAM;AAEhE,eAAS;AAAA,QACP;AAAA,QAAO;AAAA,QAAG;AAAA,QAAG;AAAA,QACb,CAAC,KAAK,MAAO,QAAM,IAAK,IAAI,IAAI;AAAA,QAChC,CAAC,KAAK,MAAO,SAAO,IAAK,IAAI,IAAI;AAAA,MACnC;AAAA,IACF;AAEA,WAAO;AAAA,MACL,MAAM;AAAA,MACN;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAEA,WAAS,cAAc,UAAU;AAC/B,QAAI,MACA,SAAS,GACT,QAAQ,WAER,aAAa;AAEjB,QAAI,OAAO,aAAa,UAAU;AAChC,aAAO;AAAA,IACT,OAAO;AACL,aAAO,SAAS;AAChB,eAAS,YAAY,WAAW,SAAS,SAAS;AAClD,mBAAa,gBAAgB,WAAW,SAAS,aAAa;AAC9D,cAAQ,WAAW,WAAW,SAAS,QAAQ;AAAA,IACjD;AAIA,QAAI,WAAW,KAAK;AACpB,QAAI,OAAO,KAAK,WAAW,QAAQ;AAEnC,QAAI,SAAS,IAAI,gBAAgB,UAAU,QAAQ;AACnD,QAAI,MAAM,OAAO,WAAW,IAAI;AAEhC,QAAI,OAAO;AACX,QAAI,OAAO,IAAI,YAAY,IAAI;AAC/B,QAAI,QAAQ,KAAK,KAAK,KAAK,yBAAyB,KAAK,qBAAqB;AAC9E,QAAI,SAAS,KAAK,KAAK,KAAK,0BAA0B,KAAK,wBAAwB;AAEnF,QAAI,UAAU;AACd,QAAI,IAAI,KAAK,wBAAwB;AACrC,QAAI,IAAI,KAAK,0BAA0B;AACvC,aAAS,UAAU;AACnB,cAAU,UAAU;AAEpB,aAAS,IAAI,gBAAgB,OAAO,MAAM;AAC1C,UAAM,OAAO,WAAW,IAAI;AAC5B,QAAI,OAAO;AACX,QAAI,YAAY;AAEhB,QAAI,SAAS,MAAM,GAAG,CAAC;AAEvB,QAAI,QAAQ,IAAI;AAEhB,WAAO;AAAA,MACL,MAAM;AAAA;AAAA,MAEN,QAAQ,OAAO,sBAAsB;AAAA,MACrC,QAAQ,CAAC,OAAO,GAAG,GAAG,OAAO,CAAC,QAAQ,QAAQ,GAAG,CAAC,SAAS,QAAQ,CAAC;AAAA,IACtE;AAAA,EACF;AAEA,EAAAF,QAAO,UAAU,WAAW;AAC1B,WAAO,eAAe,EAAE,MAAM,MAAM,SAAS;AAAA,EAC/C;AACA,EAAAA,QAAO,QAAQ,QAAQ,WAAW;AAChC,mBAAe,EAAE,MAAM;AAAA,EACzB;AACA,EAAAA,QAAO,QAAQ,SAAS;AACxB,EAAAA,QAAO,QAAQ,gBAAgB;AAC/B,EAAAA,QAAO,QAAQ,gBAAgB;AACjC,GAAG,WAAY;AACb,MAAI,OAAO,WAAW,aAAa;AACjC,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,SAAS,aAAa;AAC/B,WAAO;AAAA,EACT;AAEA,SAAO,QAAQ,CAAC;AAClB,EAAG,GAAG,QAAQ,KAAK;AAInB,IAAO,0BAAQ,OAAO;AACf,IAAI,SAAS,OAAO,QAAQ;", "names": ["module", "worker", "fettis"]}