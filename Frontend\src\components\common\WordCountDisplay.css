/* Word Count Display Component Styles */
.word-count-display {
  margin-bottom: 8px;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
  font-size: 12px;
}

.word-count-info {
  display: flex;
  gap: 16px;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 6px;
}

.word-count-text,
.char-count-text {
  color: #666;
  font-size: 11px;
}

.word-count-text strong,
.char-count-text strong {
  color: #333;
}

.word-count-error-text {
  color: #dc3545;
  font-size: 11px;
  font-weight: 500;
}

.word-count-warning-text {
  color: #fd7e14;
  font-size: 11px;
  font-weight: 500;
}

.word-count-unlimited-text {
  color: #28a745;
  font-size: 11px;
  font-weight: 500;
  font-style: italic;
}

.word-count-bar {
  height: 3px;
  background-color: #e9ecef;
  border-radius: 2px;
  overflow: hidden;
}

.word-count-progress {
  height: 100%;
  background-color: #007bff;
  transition: width 0.3s ease;
}

/* Status-based styling */
.word-count-normal {
  border-color: #28a745;
  background-color: #f8fff9;
}

.word-count-normal .word-count-progress {
  background-color: #28a745;
}

.word-count-warning {
  border-color: #fd7e14;
  background-color: #fff8f0;
}

.word-count-warning .word-count-progress {
  background-color: #fd7e14;
}

.word-count-error {
  border-color: #dc3545;
  background-color: #fff5f5;
}

.word-count-error .word-count-progress {
  background-color: #dc3545;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .word-count-info {
    gap: 12px;
  }
  
  .word-count-display {
    padding: 6px 10px;
    font-size: 11px;
  }
  
  .word-count-text,
  .char-count-text,
  .word-count-error-text,
  .word-count-warning-text,
  .word-count-unlimited-text {
    font-size: 10px;
  }
}
