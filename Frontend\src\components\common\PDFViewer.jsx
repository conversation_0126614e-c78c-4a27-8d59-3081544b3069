import { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Document, Page, pdfjs } from 'react-pdf';
import 'react-pdf/dist/esm/Page/AnnotationLayer.css';
import 'react-pdf/dist/esm/Page/TextLayer.css';

// Set worker source
pdfjs.GlobalWorkerOptions.workerSrc = '/js/pdf.worker.min.js';

const PDFViewer = ({ fileUrl }) => {
    const [numPages, setNumPages] = useState(null);
    const [pageWidth, setPageWidth] = useState(null);
    const [error, setError] = useState(false);

    useEffect(() => {
        const updatePageWidth = () => {
            // For mobile devices or small screens
            if (window.innerWidth < 768) {
                setPageWidth(window.innerWidth - 32); // Account for padding
            } else {
                setPageWidth(Math.min(800, window.innerWidth - 64)); // Max width of 800px on desktop
            }
        };

        updatePageWidth();
        window.addEventListener('resize', updatePageWidth);

        return () => {
            window.removeEventListener('resize', updatePageWidth);
        };
    }, []);

    const onDocumentLoadSuccess = ({ numPages }) => {
        setNumPages(numPages);
        setError(false);
    };

    const onDocumentLoadError = () => {
        setError(true);
    };

    if (error) {
        return (
            <div className="pdf-error-container" style={{ textAlign: 'center', padding: '20px' }}>
                <p>Failed to load PDF preview.</p>
                <a
                    href={fileUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="btn btn-primary"
                    style={{ marginTop: '10px' }}
                >
                    Open PDF in new tab
                </a>
            </div>
        );
    }

    return (
        <div className="pdf-container" style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
            <Document
                file={fileUrl}
                onLoadSuccess={onDocumentLoadSuccess}
                onLoadError={onDocumentLoadError}
                loading={<div>Loading PDF...</div>}
            >
                {Array.from(new Array(numPages), (el, index) => (
                    <Page
                        key={`page_${index + 1}`}
                        pageNumber={index + 1}
                        width={pageWidth}
                        renderTextLayer={true}
                        renderAnnotationLayer={true}
                    />
                ))}
            </Document>
        </div>
    );
};

PDFViewer.propTypes = {
    fileUrl: PropTypes.string.isRequired,
};

export default PDFViewer; 