name: Dev branch-Dev Server Deployment

on:
   push:
      branches:
         - dev

jobs:
  deploy:
    runs-on: self-hosted

    steps:
    - name: Check Directory available or not
      run: |

          cd /var/www/xosportshub-dev || { echo "Directory not found"; exit 1; }  
          echo "YES! Project is available."

    - name: Check for uncommitted changes
      id: check_changes
      run: |

        cd /var/www/xosportshub-dev

        if [[ -n $(git status --porcelain) ]]; then
          echo "Uncommitted changes found."
          echo "UNCOMMITTED_CHANGES=true" >> $GITHUB_ENV
        else
          echo "No uncommitted changes."
          echo "UNCOMMITTED_CHANGES=false" >> $GITHUB_ENV
        fi  

    - name: Commit changes if necessary
      if: env.UNCOMMITTED_CHANGES == 'true'
      run: |

        cd /var/www/xosportshub-dev

        git add .
        git commit -m "Automated commit on live dev branch $(date '+%d-%m-%Y---%H-%M')"

    - name: Checkout to dev branch if necessary
      run: |

        cd /var/www/xosportshub-dev

        CURRENT_BRANCH=$(git rev-parse --abbrev-ref HEAD)
        if [ "$CURRENT_BRANCH" != "dev" ]; then
          echo "Not on dev branch. Checking out to dev..."
          git checkout dev
        else
          echo "Already on dev branch."
        fi


    - name: Create temp branch
      run: |

         cd /var/www/xosportshub-dev
         git branch -f temp

    - name: Deploy to Development Server
      run: |

          cd /var/www/xosportshub-dev

          echo "Aborting any ongoing merge...."
          git merge --abort || true

          echo "Resetting any local changes..."
          git reset --hard HEAD || { echo "Git reset failed"; exit 1; }

          echo "Pulling latest code from dev branch..."
          git fetch origin dev || { echo "Git fetch failed"; exit 1; }
          
          echo "Resetting to origin/dev..."
          git pull origin dev || { echo "Git reset failed"; git reset --hard temp && exit 1; }

          echo "Make A New Build for a frontend"
          cd Frontend/
          npm install --legacy-peer-deps
          npm run build
          cp /var/www/htaccess/react-front-htacess ../Frontend/dist/.htaccess

          echo "Make A New Build for a backend"
          cd ../Backend/
          
          echo "Stop the service"
          sudo systemctl stop xosportshub-dev.service  

          echo "Make A npm update"
          npm update --legacy-peer-deps
          
          echo "Start the service"
          sudo systemctl start xosportshub-dev.service

          echo "Deployment completed successfully."

    - name: Commit changes if necessary
      if: env.UNCOMMITTED_CHANGES == 'true'
      run: |

        cd /var/www/xosportshub-dev

        git push origin dev         
        
