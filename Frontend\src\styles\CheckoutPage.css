/* Checkout Page Styles */
.checkout-page {
  padding: var(--heading5) var(--extrasmallfont);
  margin: auto;
  position: relative;
  overflow-x: auto;
}

.checkout-page::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="%23000" opacity="0.02"/><circle cx="75" cy="75" r="1" fill="%23000" opacity="0.02"/><circle cx="50" cy="10" r="1" fill="%23000" opacity="0.02"/><circle cx="10" cy="90" r="1" fill="%23000" opacity="0.02"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
  z-index: 0;
}

.checkout-page .checkout-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 var(--heading6);
  position: relative;
  z-index: 1;
}

.checkout-page .checkout-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--heading3);
  align-items: start;
}

/* Left Section - Checkout Form */
.checkout-page .checkout-left {
  display: flex;
  justify-content: center;
}

.checkout-page .checkout-form-container {
  background: var(--white);
  border-radius: var(--border-radius-large);
  width: 100%;

  transition: all 0.3s ease;

  overflow: hidden;

}

.checkout-page .leftborderdiv {
  padding: var(--heading4);
}

.checkout-page .checkout-title {
  font-size: var(--heading4);
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: var(--heading6);
  text-align: left;
  position: relative;
}

.checkout-page .checkout-alert {
  background-color: #fdf2f2;
  border: 1px solid #fecaca;
  border-radius: var(--border-radius);
  padding: 0.875rem 1rem;
  margin-bottom: 2rem;
  color: #dc2626;
  font-size: var(--smallfont);
  text-align: center;
}
.checkout-page .verifybtnwidth {
  width: fit-content;
}
.checkout-page .checkout-success {
  background-color: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: var(--border-radius);
  padding: 0.875rem 1rem;
  margin-bottom: 2rem;
  color: #0369a1;
  font-size: var(--smallfont);
  text-align: center;
  font-weight: 500;
}

/* Sign In Section */
.checkout-page .signin-section {
  margin-top: 2rem;
}

/* Checkout Section */
.checkout-page .checkout-section {
  margin-top: 2rem;
}

.checkout-page .section-title {
  font-size: var(--heading6);
  font-weight: 600;
  color: var(--secondary-color);
  margin-bottom: 1rem;
}

/* Saved Cards */
.checkout-page .saved-cards {
  margin-bottom: 2rem;
}

.checkout-page .card-option {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  padding: 1rem;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: border-color 0.3s ease, background-color 0.3s ease;
}

.checkout-page .card-option:hover {
  border-color: var(--btn-color);
  background-color: #fef7f7;
}

.checkout-page .card-option input[type="radio"] {
  margin-right: 1rem;
  accent-color: var(--btn-color);
}

.checkout-page .card-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  width: 100%;
}

.checkout-page .card-info {
  display: flex;

  gap: 10px;
}

.checkout-page .card-type {
  font-size: var(--smallfont);
  font-weight: 600;
  color: var(--secondary-color);
}

.checkout-page .card-number {
  font-size: var(--smallfont);
  color: var(--dark-gray);
}

/* Payment Form */
.checkout-page .payment-form {
  margin-bottom: 2rem;
}

.checkout-page .form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1rem;
}

.checkout-page .place-order-main-btn {
  width: 100%;
  padding: 0.875rem 1.5rem;
  font-size: var(--basefont);
  font-weight: 600;
}
.checkout-page .signin-sectioncontainer {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.checkout-page .signin-title {
  font-size: var(--heading6);
  font-weight: 600;
  color: var(--secondary-color);
  margin-bottom: 0.5rem;
}

.checkout-page .signin-subtitle {
  font-size: var(--smallfont);
  color: var(--dark-gray);
  margin-bottom: 1.5rem;
}

.checkout-page .signup-link {
  color: var(--btn-color);
  text-decoration: none;
  font-weight: 500;
}

.checkout-page .signup-link:hover {
  text-decoration: underline;
}

.checkout-page .signin-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.checkout-page .form-group {
  display: flex;
  flex-direction: column;
}

.checkout-page .input-with-icon {
  position: relative;
  display: flex;
  align-items: center;
}

.checkout-page .input-icon {
  position: absolute;
  left: 1rem;
  color: var(--dark-gray);
  font-size: var(--heading6);
  z-index: 1;
  pointer-events: none;
}

.checkout-page .form-input {
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--basefont);
  color: var(--text-color);
  background-color: var(--white);
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
  outline: none;
  width: 100%;
}

.checkout-page .form-input:focus {
  border-color: var(--dark-gray);
}

.checkout-page .form-input::placeholder {
  color: var(--dark-gray);
  opacity: 0.7;
}

/* Phone Input Styles - Exact copy from Auth.css */
.checkout-page .auth-form-input {
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
  width: 100%;
}

.checkout-page .form-input-container {
  position: relative;
}

.checkout-page .phone-input-wrapper {
  display: flex;
  width: 100%;
}

.checkout-page .country-code-select {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 12px 10px;
  border: 1px solid var(--light-gray);
  border-right: none;
  border-radius: var(--border-radius) 0 0 var(--border-radius);
  background-color: var(--white);
  color: var(--text-color);
  font-size: var(--basefont);
  cursor: pointer;
}

.checkout-page .country-code-select:focus {
  border-color: #ff5630;
  outline: none;
}

.checkout-page .phone-input {
  flex: 1;
  border-radius: 0 var(--border-radius) var(--border-radius) 0 !important;
  padding: 12px 16px;
  border: 1px solid var(--light-gray);
  font-size: var(--basefont);
  color: var(--text-color);
  background-color: var(--white);
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.checkout-page .phone-input:focus {
  border-color: #ff5630;
  box-shadow: 0 0 0 2px rgba(255, 86, 48, 0.2);
  outline: none;
}

.checkout-page .phone-input::placeholder {
  color: var(--dark-gray);
  opacity: 0.7;
}

.checkout-page .selectstylesnone {
  border: none;
  outline: none;
  background: transparent;
  color: var(--text-color);
  font-size: var(--basefont);
  cursor: pointer;
  margin-left: 8px;
}

.checkout-page .selectstylesnone:focus {
  outline: none;
}

.checkout-page .input-error {
  border-color: #ff3b30;
}

.checkout-page .error-message {
  color: #ff3b30;
  font-size: var(--extrasmallfont);
  margin-top: 5px;
}

/* Right Section - Order Summary */
.checkout-page .checkout-right {
  display: flex;
  justify-content: center;
  width: 100%;
}

.checkout-page .order-summary {
  background: var(--white);
  border-radius: var(--border-radius-large);
  width: 100%;
  max-width: 500px;

  transition: all 0.3s ease;

  overflow: hidden;
  position: sticky;
  top: var(--heading5);
}

.checkout-page .rightbackgrounddiv {
background-color: var(--primary-light-color);
  padding: var(--heading4);
  border-radius: var(--border-radius-large);
}

.checkout-page .order-title {
  font-size: var(--heading4);
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: var(--heading6);
  text-align: left;
  position: relative;
}

/* Item Info Section */

/* Item Info Header with Dropdown */
.checkout-page .item-info-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  padding-bottom: var(--smallfont);
  margin-bottom: var(--basefont);
  border-bottom: 2px solid var(--light-gray);
  transition: all 0.3s ease;
}


.checkout-page .item-info-title {
  font-size: var(--heading5);
  font-weight: 600;
  color: var(--txt-color);
  margin: 0;
  flex: 1;
}

/* Dropdown Toggle Button */
.checkout-page .dropdown-toggle {
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--extrasmallfont);
  border-radius: var(--border-radius);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--secondary-color);
}

.checkout-page .dropdown-toggle:hover {
  background-color: var(--primary-light-color);
  color: var(--btn-color);
}

/* Dropdown Arrow */
.checkout-page .dropdown-arrow {
  transition: transform 0.3s ease;
  color: inherit;
}

.checkout-page .dropdown-arrow.expanded {
  transform: rotate(180deg);
}

/* Item Content Container with Animation (includes both item details and order info) */
.checkout-page .item-content-container {
  overflow: hidden;
  transition: all 0.3s ease;
}

.checkout-page .item-content-container.expanded {
  max-height: 500px;
  opacity: 1;
}

.checkout-page .item-content-container.collapsed {
  max-height: 0;
  opacity: 0;
  margin-bottom: 0;
}



.checkout-page .item-details {
  display: flex;
  gap: var(--basefont);
  align-items: flex-start;
}

.checkout-page .item-image {
  flex-shrink: 0;
  position: relative;
}

.checkout-page .item-thumbnail {
  width: 80px;
  height: 80px;
  border-radius: var(--border-radius-medium);
  object-fit: cover;
  border: 2px solid var(--light-gray);
  box-shadow: var(--box-shadow-light);
  transition: all 0.3s ease;
}

.checkout-page .item-thumbnail:hover {
  transform: scale(1.05);
  box-shadow: var(--box-shadow);
}

.checkout-page .item-description {
  flex: 1;
  overflow: hidden;
}

.checkout-page .item-name {
  font-size: var(--basefont);
  font-weight: 600;
  color: var(--text-color);

  
  overflow: hidden;
  text-overflow: ellipsis !important;
}

/* Pricing Section */

.checkout-page .price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
padding: 1rem 0;

  border-top: 1px dashed var(--light-gray);
  transition: all 0.3s ease;
}

.checkout-page .price-row:last-child {
  margin-bottom: 0;
  border-bottom: none;
}


.checkout-page .price-label, .checkout-page .price-value {
  font-size: var(--basefont);
  color: var(--secondary-color);
  font-weight: 400;
}



.checkout-page .total-row .price-label,
.checkout-page .total-row .price-value {
  color: var(--text-color);
 font-size: var(--heading6);
  font-weight: 600;
}

/* Code Verification Modal Styles */
.checkout-page .verification-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-index-modal);
  padding: 1rem;
}

.checkout-page .verification-modal {
  background-color: var(--white);
  border-radius: var(--border-radius-large);
  box-shadow: var(--box-shadow-dark);
  width: 100%;
  max-width: 400px;
  max-height: 90vh;
  overflow-y: auto;
}

.checkout-page .verification-modal-content {
  padding: 2rem;
  text-align: center;
}

.checkout-page .verification-title {
  font-size: var(--heading5);
  font-weight: 600;
  color: var(--secondary-color);
  margin-bottom: 0.5rem;
}

.checkout-page .verification-subtitle {
  font-size: var(--smallfont);
  color: var(--dark-gray);
  margin-bottom: 2rem;
  line-height: 1.4;
}

.checkout-page .verification-code-container {
  display: flex;
  justify-content: center;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.checkout-page .verification-code-input {
  width: 3rem;
  height: 3rem;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  text-align: center;
  font-size: var(--heading6);
  font-weight: 600;
  color: var(--text-color);
  background-color: var(--white);
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
  outline: none;
}

.checkout-page .verification-code-input:focus {
  border-color: var(--btn-color);
  box-shadow: 0 0 0 2px rgba(238, 52, 37, 0.1);
}

.checkout-page .verification-code-input:not(:placeholder-shown) {
  border-color: var(--btn-color);
}

.checkout-page .verification-error {
  color: var(--error-color);
  font-size: var(--extrasmallfont);
  margin-bottom: 1rem;
  text-align: center;
}

.checkout-page .verification-resend {
  font-size: var(--smallfont);
  color: var(--dark-gray);
  margin-bottom: 2rem;
}

.checkout-page .resend-link {
  color: var(--btn-color);
  cursor: pointer;
  font-weight: 500;
  text-decoration: underline;
}

.checkout-page .resend-link:hover {
  color: var(--primary-color);
}

.checkout-page .verification-buttons {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.checkout-page .verification-submit-btn {
  width: 100%;
  padding: 0.875rem 1.5rem;
  font-size: var(--basefont);
  font-weight: 600;
}

.checkout-page .verification-back-btn {
  background: transparent;
  border: none;
  color: var(--btn-color);
  font-size: var(--smallfont);
  font-weight: 500;
  cursor: pointer;
  padding: 0.5rem;
  transition: color 0.3s ease;
}

.checkout-page .verification-back-btn:hover {
  color: var(--primary-color);
  text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .checkout-page .checkout-container {
    max-width: 100%;
    padding: 0 1.5rem;
  }
}

@media (max-width: 1024px) {
  .checkout-page .checkout-container {
    padding: 0 var(--smallfont);
  }

  .checkout-page .checkout-content {
    gap: 1rem;
  }
  .checkout-page .item-thumbnail {
    width: 60px;
    height: 60px;
  }

  /* Dropdown responsive adjustments */
  .checkout-page .dropdown-toggle {
    padding: var(--extrasmallfont);
  }

  .checkout-page .item-content-container.expanded {
    max-height: 450px;
  }
}

@media (max-width: 768px) {
  .checkout-page .checkout-content {
    grid-template-columns: 1fr;
    gap: var(--heading5);
  }

  .checkout-page .checkout-form-container {
    max-width: 100%;
    order: 2;
  }

  .checkout-page .checkout-right {
    order: 1;
  }

  .checkout-page .order-summary {
    position: static;
    max-width: 100%;
  }

  .checkout-page .checkout-title,
  .checkout-page .order-title {
    font-size: var(--heading5);
  }

  .checkout-page .item-details {
    gap: var(--smallfont);
  }

  .checkout-page .item-thumbnail {
    width: 60px;
    height: 60px;
  }

  .checkout-page .leftborderdiv,
  .checkout-page .rightbackgrounddiv {
    padding: var(--heading5);
  }

  /* Checkout Section Responsive */
  .checkout-page .form-row {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .checkout-page .card-option {
    padding: 0.75rem;
  }

  /* Verification Modal Responsive */
  .checkout-page .verification-modal {
    max-width: 350px;
  }

  .checkout-page .verification-modal-content {
    padding: 1.5rem;
  }

  .checkout-page .verification-code-container {
    gap: 0.5rem;
  }

  .checkout-page .verification-code-input {
    width: 2.5rem;
    height: 2.5rem;
    font-size: var(--basefont);
  }
}

@media (max-width: 640px) {
  .checkout-page {
    padding: var(--basefont) 0;
  }

  .checkout-page .checkout-container {
    padding: 0 1rem;
  }

  .checkout-page .checkout-content {
    gap: 1rem;
  }
}

@media (max-width: 480px) {
  .checkout-page .checkout-container {
    padding: 0 0.75rem;
  }

  .checkout-page .checkout-title,
  .checkout-page .order-title {
    font-size: var(--heading6);
    margin-bottom: 1rem;
  }

  .checkout-page .item-thumbnail {
    width: 50px;
    height: 50px;
  }

  .checkout-page .item-name {
    font-size: var(--extrasmallfont);
  }

  /* Mobile dropdown adjustments */
  .checkout-page .item-info-title {
    font-size: var(--basefont);
  }

  .checkout-page .dropdown-toggle {
    padding: 0.25rem;
  }

  .checkout-page .item-content-container.expanded {
    max-height: 400px;
  }

  .checkout-page .form-input {
    font-size: var(--smallfont);
    padding: 0.75rem 1rem;
  }

  .checkout-page .phone-input {
    font-size: var(--smallfont);
    padding: 0.75rem 1rem;
  }

  /* Checkout Section Mobile */
  .checkout-page .card-option {
    padding: 0.5rem;
  }

  .checkout-page .card-type,
  .checkout-page .card-number {
    font-size: var(--extrasmallfont);
  }

  .checkout-page .section-title {
    font-size: var(--basefont);
  }

  .checkout-page .country-code-select {
    padding: 10px 8px;
    font-size: calc(var(--basefont) - 1px);
  }

  .checkout-page .selectstylesnone {
    font-size: calc(var(--basefont) - 1px);
    margin-left: 6px;
  }

  /* Verification Modal Mobile */
  .checkout-page .verification-modal {
    max-width: 300px;
  }

  .checkout-page .verification-modal-content {
    padding: 1.25rem;
  }

  .checkout-page .verification-title {
    font-size: var(--heading6);
  }

  .checkout-page .verification-subtitle {
    font-size: var(--extrasmallfont);
  }

  .checkout-page .verification-code-container {
    gap: 0.375rem;
  }

  .checkout-page .verification-code-input {
    width: 2.25rem;
    height: 2.25rem;
    font-size: var(--smallfont);
  }

  .checkout-page .verification-submit-btn {
    padding: 0.75rem 1.25rem;
    font-size: var(--smallfont);
  }
}

@media (max-width: 360px) {
  .checkout-page .checkout-container {
    padding: 0 0.5rem;
  }

  .checkout-page .country-code-select {
    padding: 8px 6px;
  }

  .checkout-page .selectstylesnone {
    font-size: calc(var(--basefont) - 2px);
    margin-left: 4px;
  }

  .checkout-page .item-details {
    gap: 0.5rem;
  }

  .checkout-page .item-thumbnail {
    width: 60px;
    height: 60px;
    margin: 0 auto;
  }

  /* Verification Modal Extra Small */
  .checkout-page .verification-modal {
    max-width: 280px;
  }

  .checkout-page .verification-modal-content {
    padding: 1rem;
  }

  .checkout-page .verification-code-container {
    gap: 0.25rem;
  }

  .checkout-page .verification-code-input {
    width: 2rem;
    height: 2rem;
    font-size: var(--extrasmallfont);
  }
}

/* Additional Styles for New Payment Flow */

/* Order Already Paid */
.checkout-page .order-already-paid {
  text-align: center;
  padding: 3rem 2rem;
  background: white;
  border-radius: var(--border-radius-large);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  display: grid;
  justify-items: center;
  align-items: center;
}

.checkout-page .order-already-paid h2 {
  font-size: var(--heading4);
  color: var(--secondary-color);
  margin-bottom: 1rem;
}

.checkout-page .order-already-paid p {
  color: var(--dark-gray);
  margin-bottom: 2rem;
}

/* Payment Success/Error States */
.checkout-page .payment-success,
.checkout-page .payment-error {
  text-align: center;
  padding: 2rem;
  border-radius: var(--border-radius-large);
  margin: 2rem 0;
  display: grid;
  justify-items: center;
}

.checkout-page .payment-success {
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  color: #0369a1;
}

.checkout-page .payment-error {
  background: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
}

.checkout-page .success-icon,
.checkout-page .error-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.checkout-page .payment-success h3,
.checkout-page .payment-error h3 {
  font-size: var(--heading5);
  margin-bottom: 1rem;
}

.checkout-page .retry-btn {
  margin-top: 1rem;
  padding: 0.75rem 1.5rem;
  background: var(--btn-color);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  font-weight: 500;
}

/* Order Info Section */


.checkout-page .order-info-title {
  font-size: var(--heading6);
  font-weight: 600;
  color: var(--secondary-color);
  margin-bottom: 1rem;
}

.checkout-page .order-details {
  display: flex;
  flex-direction: column;

}

.checkout-page .order-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0;
  border-top: 1px dashed var(--light-gray);
  font-weight: 400;
  font-size: var(--basefont);
}

.checkout-page .order-row span:first-child,
.checkout-page .order-row span:last-child {
  color: var(--secondary-color);
}



.checkout-page .status {
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: var(--extrasmallfont);
  font-weight: 500;
  text-transform: uppercase;
}

.checkout-page .status.pending {
  background: #fef3c7;
  color: #d97706 !important;
}

.checkout-page .status.completed {
  background: #d1fae5;
  color: #059669 !important;
}

.checkout-page .status.failed {
  background: #fee2e2 ;
  color: #dc2626 !important;
}
.checkout-page .status.expired {
  background: #fee2e2;
  color: #dc2626 !important;
}
/* Additional content info */
.checkout-page .item-coach {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
  margin: 0.25rem 0;
}

.checkout-page .item-type {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
  margin: 0;
}
