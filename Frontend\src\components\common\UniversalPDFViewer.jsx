import React, { useState, useEffect } from 'react';
import { Document, Page, pdfjs } from 'react-pdf';
import { FaExclamationTriangle, FaChevronLeft, FaChevronRight } from 'react-icons/fa';
import 'react-pdf/dist/esm/Page/AnnotationLayer.css';
import 'react-pdf/dist/esm/Page/TextLayer.css';
import '../../styles/UniversalPDFViewer.css';

// Set worker source directly to CDN
pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;

const UniversalPDFViewer = ({
  fileUrl,
  fileName = '',
  title = 'PDF Document',
  className = '',
  height = '100%',
  showDownload = false,
  onDownload = null,
  showNativeOptions = false
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [pageWidth, setPageWidth] = useState(null);
  const [numPages, setNumPages] = useState(null);
  const [pageNumber, setPageNumber] = useState(1);

  useEffect(() => {
    const updatePageWidth = () => {
      if (window.innerWidth < 768) {
        setPageWidth(window.innerWidth - 32);
      } else {
        setPageWidth(Math.min(800, window.innerWidth - 64));
      }
    };

    updatePageWidth();
    window.addEventListener('resize', updatePageWidth);

    return () => {
      window.removeEventListener('resize', updatePageWidth);
    };
  }, []);

  const onDocumentLoadSuccess = ({ numPages }) => {
    setIsLoading(false);
    setHasError(false);
    setNumPages(numPages);
  };

  const onDocumentLoadError = (error) => {
    console.error('PDF load error:', error);
    setIsLoading(false);
    setHasError(true);
  };

  const handlePrevPage = () => {
    setPageNumber(prev => Math.max(prev - 1, 1));
  };

  const handleNextPage = () => {
    setPageNumber(prev => Math.min(prev + 1, numPages || 1));
  };

  // Main PDF viewer with react-pdf
  return (
    <div className={`universal-pdf-viewer ${className}`} style={{ height }}>
      {/* Pagination Controls */}
      {numPages > 1 && (
        <div className="universal-pdf-viewer__controls">
          <button
            className="universal-pdf-viewer__nav-btn"
            onClick={handlePrevPage}
            disabled={pageNumber <= 1}
          >
            <FaChevronLeft />
          </button>
          <span className="universal-pdf-viewer__page-info">
            Page {pageNumber} of {numPages}
          </span>
          <button
            className="universal-pdf-viewer__nav-btn"
            onClick={handleNextPage}
            disabled={pageNumber >= numPages}
          >
            <FaChevronRight />
          </button>
        </div>
      )}

      <div className="universal-pdf-viewer__document">
        {isLoading && (
          <div className="universal-pdf-viewer__loading">
            <div className="universal-pdf-viewer__spinner"></div>
            <p>Loading PDF...</p>
          </div>
        )}

        {hasError ? (
          <div className="universal-pdf-viewer__error">
            <FaExclamationTriangle className="universal-pdf-viewer__warning-icon" />
            <h3>PDF Preview Not Available</h3>
            <p>Unable to load the PDF preview</p>
          </div>
        ) : (
          <Document
            file={fileUrl}
            onLoadSuccess={onDocumentLoadSuccess}
            onLoadError={onDocumentLoadError}
            loading=""
            error=""
          >
            <Page
              pageNumber={pageNumber}
              width={pageWidth}
              renderTextLayer={true}
              renderAnnotationLayer={true}
            />
          </Document>
        )}
      </div>
    </div>
  );
};

export default UniversalPDFViewer; 