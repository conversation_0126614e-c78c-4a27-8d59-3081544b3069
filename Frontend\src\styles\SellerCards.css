/* SellerCards Component Styles */
.SellerCards {
  padding: 20px 20px;
  background-color: var(--white);

  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius-large);
}

/* Header Section */
.SellerCards__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--light-gray);
}

.SellerCards__subtitle {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
}
.SellerCards .selleraddcard {
  display: flex;
  align-items: center;
  gap: var(--extrasmallfont);
  background-color: transparent;
  color: var(--btn-color);
  border: 1px solid var(--btn-color);
  padding: var(--extrasmallfont) var(--basefont);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  font-size: var(--smallfont);
}

/* Cards List View */
.SellerCards__cards-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.SellerCards__card-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  background-color: var(--white);
  transition: box-shadow 0.2s ease;
}

.SellerCards__card-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.SellerCards__card-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.SellerCards__card-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.SellerCards__card-logo {
  width: 50px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.SellerCards__card-logo img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.SellerCards__card-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.SellerCards__card-number {
  font-size: var(--basefont);
  font-weight: 500;
  color: var(--text-color);
  letter-spacing: 1px;
}

.SellerCards__card-name {
  font-size: var(--smallfont);
  color: var(--dark-gray);
  font-weight: 400;
}

.SellerCards__card-expiry {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
  font-weight: 400;
}

.SellerCards__card-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.SellerCards__default-btn {
  background: none;
  border: none;
  color: #ffc107;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  font-size: 16px;
}

.SellerCards__default-btn:hover:not(:disabled) {
  background-color: rgba(255, 193, 7, 0.1);
  transform: scale(1.1);
}

.SellerCards__default-btn.active {
  color: #ffc107;
}

.SellerCards__default-btn:disabled {
  cursor: default;
}

.SellerCards__delete-btn {
  color: black;
  background-color: transparent;
  border: none;
}

.SellerCards__delete-btn:hover {
  transform: scale(1.02);
  color: black;
  background-color: transparent;
}

/* Empty State */
.SellerCards__empty-state {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 60px 20px;
  color: var(--dark-gray);
  gap: 16px;
}

.SellerCards__empty-state p {
  font-size: var(--basefont);
  margin: 0;
}

.SellerCards__empty-icon {
  font-size: 48px;
  color: var(--light-gray);
  margin-bottom: 8px;
}

.SellerCards__add-first-btn {
  background-color: var(--btn-color);
  color: var(--white);
  border: none;
  padding: 12px 24px;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  font-size: var(--basefont);
}

.SellerCards__add-first-btn:hover {
  background: linear-gradient(
    to bottom,
    var(--primary-color),
    var(--btn-color)
  );
  transform: scale(1.02);
  box-shadow: 0 4px 8px rgba(238, 52, 37, 0.3);
}

.SellerCards__loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  font-size: var(--basefont);
  color: var(--dark-gray);
}

/* Add Card Form */
.SellerCards__form {
  margin: 0 auto;
}

.SellerCards__form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
}

.SellerCards__input-field {
  flex: 1;
}

.SellerCards__input-field--full {
  flex: 1 1 100%;
}

.SellerCards__input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.SellerCards__input-icon {
  position: absolute;
  left: 12px;
  color: var(--dark-gray);
  font-size: 16px;
  z-index: 1;
}

.SellerCards__input {
  width: 100%;
  padding: 12px 12px 12px 40px;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--basefont);
  font-family: "Poppins", sans-serif;
  transition: border-color 0.2s ease;
  background-color: var(--white);
}

.SellerCards__input:focus {
  outline: none;
  border-color: var(--btn-color);
  box-shadow: 0 0 0 2px rgba(255, 107, 53, 0.1);
}

.SellerCards__input::placeholder {
  color: var(--dark-gray);
}

.SellerCards__stripe-element {
  width: 100%;
  font-size: var(--basefont);
  color: var(--text-color);
  background-color: transparent;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);

  padding: 12px 12px 12px 40px;
  min-height: 20px;
}

/* Form Actions */
.SellerCards__form-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin-top: 24px;
}

.SellerCards__cancel-btn {
  background-color: transparent;
  color: var(--dark-gray);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius-medium);
  padding: 12px 32px;
  font-size: var(--basefont);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.SellerCards__cancel-btn:hover {
  background-color: var(--bg-gray);
  border-color: var(--dark-gray);
}

/* Responsive Design */
@media (max-width: 768px) {
  .SellerCards {
    padding: 16px;
  }

  .SellerCards__card-item {
    padding: 16px;
  }

  .SellerCards__card-content {
    gap: 12px;
  }

  .SellerCards__card-logo {
    width: 40px;
    height: 26px;
  }

  .SellerCards__form-row {
    flex-direction: column;
    gap: 12px;
  }
}
@media (max-width: 480px) {
  .SellerCards__header .btn-outline {
    padding: 10px 10px;
  }
}
@media (max-width: 350px) {
  .SellerCards__card-info,
  .SellerCards__card-actions {
    display: grid;
  }
  .SellerCards__form-actions {
    flex-direction: column;
  }
  .SellerCards__header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }
}
