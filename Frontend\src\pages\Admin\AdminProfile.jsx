import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { selectProfile, updateProfile as updateAdminProfile } from "../../redux/slices/adminDashboardSlice";
import { updateCurrentUser, uploadProfileImage, getCurrentUser } from "../../redux/slices/authSlice";
import AdminLayout from "../../components/admin/AdminLayout";
import { FaUser, FaCamera } from "react-icons/fa";
import { toast } from "react-toastify";
import "../../styles/AdminProfile.css";
import { IMAGE_BASE_URL } from "../../utils/constants";

const AdminProfile = () => {
    const dispatch = useDispatch();
    const profile = useSelector(selectProfile);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [selectedFile, setSelectedFile] = useState(null);
    const [formData, setFormData] = useState({
        firstName: profile?.firstName || '',
        lastName: profile?.lastName || '',
        email: profile?.email || '',
        mobile: profile?.phone || '',
        profileImage: profile?.profileImage || null
    });

    // Fetch current user data when component mounts
    useEffect(() => {
        const fetchUserData = async () => {
            try {
                const result = await dispatch(getCurrentUser()).unwrap();
                if (result.data) {
                    // Update admin profile state with user data
                    dispatch(updateAdminProfile({
                        firstName: result.data.firstName,
                        lastName: result.data.lastName,
                        email: result.data.email,
                        phone: result.data.mobile,
                        profileImage: result.data.profileImage
                    }));
                    // Update form data
                    setFormData({
                        firstName: result.data.firstName,
                        lastName: result.data.lastName,
                        email: result.data.email,
                        mobile: result.data.mobile,
                        profileImage: result.data.profileImage
                    });
                }
            } catch (error) {
                console.error('Error fetching user data:', error);
            }
        };
        fetchUserData();
    }, [dispatch]);

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    const handleImageChange = (e) => {
        const file = e.target.files[0];
        if (file) {
            if (file.size > 5 * 1024 * 1024) { // 5MB limit
                toast.error("Image size should be less than 5MB");
                return;
            }
            setSelectedFile(file);
            // Create preview
            const reader = new FileReader();
            reader.onloadend = () => {
                setFormData(prev => ({
                    ...prev,
                    profileImage: reader.result
                }));
            };
            reader.readAsDataURL(file);
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setIsSubmitting(true);

        try {
            let profileImageUrl = formData.profileImage;

            // Upload image first if a new file is selected
            if (selectedFile) {
                const uploadResult = await dispatch(uploadProfileImage(selectedFile)).unwrap();
                if (uploadResult.success && uploadResult.data.fileUrl) {
                    profileImageUrl = uploadResult.data.fileUrl;
                }
            }

            // Update user profile
            const updateData = {
                firstName: formData.firstName,
                lastName: formData.lastName,
                mobile: formData.mobile,
                profileImage: profileImageUrl
            };

            const result = await dispatch(updateCurrentUser(updateData)).unwrap();

            if (result.success) {
                // Update admin dashboard state
                dispatch(updateAdminProfile({
                    firstName: formData.firstName,
                    lastName: formData.lastName,
                    phone: formData.mobile,
                    profileImage: profileImageUrl
                }));

                // Refresh user data to ensure everything is in sync
                await dispatch(getCurrentUser()).unwrap();

                toast.success("Profile updated successfully!");
                setSelectedFile(null);
            } else {
                throw new Error(result.message || "Failed to update profile");
            }
        } catch (error) {
            console.error('Profile update error:', error);
            toast.error(error.message || "Failed to update profile");
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <AdminLayout>
            <div className="AdminProfile">
                <h2>My Profile</h2>

                <div className="AdminProfile__content">
                    <form onSubmit={handleSubmit} className="AdminProfile__form">
                        <div className="AdminProfile__fields">
                            <div className="AdminProfile__field-group">
                                <label>First Name</label>
                                <input
                                    type="text"
                                    name="firstName"
                                    value={formData.firstName}
                                    onChange={handleInputChange}
                                    disabled={isSubmitting}
                                    required
                                />
                            </div>

                            <div className="AdminProfile__field-group">
                                <label>Last Name</label>
                                <input
                                    type="text"
                                    name="lastName"
                                    value={formData.lastName}
                                    onChange={handleInputChange}
                                    disabled={isSubmitting}
                                    required
                                />
                            </div>

                            <div className="AdminProfile__field-group">
                                <label>Email</label>
                                <input
                                    type="email"
                                    name="email"
                                    value={formData.email}
                                    onChange={handleInputChange}
                                    disabled={true}
                                />
                            </div>

                            <div className="AdminProfile__field-group">
                                <label>Phone</label>
                                <input
                                    type="tel"
                                    name="mobile"
                                    value={formData.mobile}
                                    onChange={handleInputChange}
                                    disabled={isSubmitting}
                                    pattern="[0-9+\-\s()]+"
                                    title="Please enter a valid phone number"
                                    placeholder="+****************"
                                />
                            </div>

                            <button
                                type="submit"
                                className={`AdminProfile__save-btn ${isSubmitting ? 'loading' : ''}`}
                                disabled={isSubmitting}
                            >
                                Update & Save
                            </button>
                        </div>

                        <div className="AdminProfile__image-section">
                            <h3>Profile Image</h3>
                            <div className="AdminProfile__image-container">
                                {formData.profileImage ? (
                                    <img src={formData.profileImage.startsWith('/upload') ? IMAGE_BASE_URL + formData.profileImage : formData.profileImage} alt="Profile" />
                                ) : (
                                    <FaUser className="AdminProfile__default-avatar" />
                                )}
                            </div>
                            <label className="AdminProfile__upload-btn">
                                <FaCamera /> Upload Photo
                                <input
                                    type="file"
                                    accept="image/*"
                                    onChange={handleImageChange}
                                    style={{ display: 'none' }}
                                    disabled={isSubmitting}
                                />
                            </label>
                        </div>
                    </form>
                </div>
            </div>
        </AdminLayout>
    );
};

export default AdminProfile; 